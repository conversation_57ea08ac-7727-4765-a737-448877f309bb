"""
文档处理异步流水线

实现文档处理的完整异步流水线：
上传 → 解析 → 分块 → 向量化 → 入库

基于事件驱动架构，每个步骤都是独立的异步任务
"""

import asyncio
import logging
import os
import tempfile
from typing import Dict, Any, List, Optional
from pathlib import Path

from .event_bus import EventBus, Event, EventType, EventHandler, EventPriority, publish_event
from .task_queue import TaskQueue, Task, TaskProcessor, TaskPriority

logger = logging.getLogger(__name__)


class DocumentUploadProcessor(TaskProcessor):
    """文档上传处理器"""
    
    def __init__(self):
        super().__init__(["document.upload"])
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """处理文档上传任务"""
        payload = task.payload
        document_id = payload["document_id"]
        file_path = payload["file_path"]
        user_id = payload.get("user_id")
        topic_id = payload.get("topic_id")
        
        logger.info(f"Processing document upload: {document_id}")
        
        # 验证文件存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)
        
        # 发布文档上传完成事件
        await publish_event(
            event_type=EventType.DOCUMENT_UPLOADED,
            payload={
                "document_id": document_id,
                "file_path": file_path,
                "file_name": file_name,
                "file_size": file_size,
                "user_id": user_id,
                "topic_id": topic_id
            },
            source_service="document_service",
            user_id=user_id,
            correlation_id=task.metadata.correlation_id
        )
        
        return {
            "document_id": document_id,
            "file_path": file_path,
            "file_size": file_size,
            "status": "uploaded"
        }


class DocumentParseProcessor(TaskProcessor):
    """文档解析处理器"""
    
    def __init__(self):
        super().__init__(["document.parse"])
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """处理文档解析任务"""
        payload = task.payload
        document_id = payload["document_id"]
        file_path = payload["file_path"]
        user_id = payload.get("user_id")
        
        logger.info(f"Processing document parse: {document_id}")
        
        # 根据文件扩展名选择解析方法
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext in ['.txt', '.md']:
            content = await self._parse_text_file(file_path)
        elif file_ext == '.pdf':
            content = await self._parse_pdf_file(file_path)
        elif file_ext in ['.doc', '.docx']:
            content = await self._parse_word_file(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        # 发布文档解析完成事件
        await publish_event(
            event_type=EventType.DOCUMENT_PARSED,
            payload={
                "document_id": document_id,
                "content": content,
                "content_length": len(content),
                "user_id": user_id
            },
            source_service="document_service",
            user_id=user_id,
            correlation_id=task.metadata.correlation_id
        )
        
        return {
            "document_id": document_id,
            "content": content,
            "content_length": len(content),
            "status": "parsed"
        }
    
    async def _parse_text_file(self, file_path: str) -> str:
        """解析文本文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    async def _parse_pdf_file(self, file_path: str) -> str:
        """解析PDF文件"""
        # 这里应该使用PDF解析库，如PyPDF2或pdfplumber
        # 为了简化，暂时返回占位符
        return f"[PDF content from {file_path}]"
    
    async def _parse_word_file(self, file_path: str) -> str:
        """解析Word文件"""
        # 这里应该使用Word解析库，如python-docx
        # 为了简化，暂时返回占位符
        return f"[Word content from {file_path}]"


class DocumentChunkProcessor(TaskProcessor):
    """文档分块处理器"""
    
    def __init__(self, chunk_size: int = 1000, overlap_size: int = 100):
        super().__init__(["document.chunk"])
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """处理文档分块任务"""
        payload = task.payload
        document_id = payload["document_id"]
        content = payload["content"]
        user_id = payload.get("user_id")
        
        logger.info(f"Processing document chunk: {document_id}")
        
        # 执行文档分块
        chunks = await self._chunk_text(content)
        
        # 发布文档分块完成事件
        await publish_event(
            event_type=EventType.DOCUMENT_CHUNKED,
            payload={
                "document_id": document_id,
                "chunks": chunks,
                "chunk_count": len(chunks),
                "user_id": user_id
            },
            source_service="document_service",
            user_id=user_id,
            correlation_id=task.metadata.correlation_id
        )
        
        return {
            "document_id": document_id,
            "chunks": chunks,
            "chunk_count": len(chunks),
            "status": "chunked"
        }
    
    async def _chunk_text(self, text: str) -> List[str]:
        """将文本分块"""
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            
            # 如果不是最后一块，尝试在单词边界处分割
            if end < len(text):
                # 向后查找空格或标点符号
                for i in range(end, max(start + self.chunk_size // 2, end - 100), -1):
                    if text[i] in ' \n\t.,!?;:':
                        end = i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # 计算下一个起始位置（考虑重叠）
            start = max(start + 1, end - self.overlap_size)
        
        return chunks


class DocumentVectorizeProcessor(TaskProcessor):
    """文档向量化处理器"""
    
    def __init__(self, embedding_service_url: str = "http://localhost:9001"):
        super().__init__(["document.vectorize"])
        self.embedding_service_url = embedding_service_url
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """处理文档向量化任务"""
        payload = task.payload
        document_id = payload["document_id"]
        chunks = payload["chunks"]
        user_id = payload.get("user_id")
        
        logger.info(f"Processing document vectorize: {document_id}, chunks: {len(chunks)}")
        
        # 调用向量化服务
        embeddings = await self._create_embeddings(chunks)
        
        # 发布文档向量化完成事件
        await publish_event(
            event_type=EventType.DOCUMENT_VECTORIZED,
            payload={
                "document_id": document_id,
                "chunks": chunks,
                "embeddings": embeddings,
                "user_id": user_id
            },
            source_service="document_service",
            user_id=user_id,
            correlation_id=task.metadata.correlation_id
        )
        
        return {
            "document_id": document_id,
            "embeddings": embeddings,
            "embedding_count": len(embeddings),
            "status": "vectorized"
        }
    
    async def _create_embeddings(self, chunks: List[str]) -> List[List[float]]:
        """创建文本向量"""
        # 这里应该调用embedding_service
        # 为了简化，返回模拟数据
        import random
        embeddings = []
        for chunk in chunks:
            # 生成模拟向量：使用统一配置的向量维度，避免硬编码
            try:
                from manticore_search.utils.config import get_settings as get_manticore_settings
                vector_dims = get_manticore_settings().vector_dimensions
            except Exception:
                # 回退到常见的embedding维度以保证测试可运行
                vector_dims = 1536
            embedding = [random.random() for _ in range(vector_dims)]
            embeddings.append(embedding)
        
        await asyncio.sleep(0.1)  # 模拟网络延迟
        return embeddings


class DocumentIndexProcessor(TaskProcessor):
    """文档索引处理器"""
    
    def __init__(self, manticore_service_url: str = "http://localhost:9000"):
        super().__init__(["document.index"])
        self.manticore_service_url = manticore_service_url
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """处理文档索引任务"""
        payload = task.payload
        document_id = payload["document_id"]
        chunks = payload["chunks"]
        embeddings = payload["embeddings"]
        user_id = payload.get("user_id")
        
        logger.info(f"Processing document index: {document_id}")
        
        # 将向量数据存储到Manticore Search
        indexed_count = await self._store_to_index(document_id, chunks, embeddings)
        
        # 发布文档索引完成事件
        await publish_event(
            event_type=EventType.DOCUMENT_INDEXED,
            payload={
                "document_id": document_id,
                "indexed_count": indexed_count,
                "user_id": user_id
            },
            source_service="document_service",
            user_id=user_id,
            correlation_id=task.metadata.correlation_id
        )
        
        return {
            "document_id": document_id,
            "indexed_count": indexed_count,
            "status": "indexed"
        }
    
    async def _store_to_index(self, document_id: str, chunks: List[str], embeddings: List[List[float]]) -> int:
        """存储到搜索索引"""
        # 这里应该调用manticore_search服务
        # 为了简化，模拟存储过程
        await asyncio.sleep(0.2)  # 模拟存储延迟
        return len(chunks)


class DocumentPipelineEventHandler(EventHandler):
    """文档处理流水线事件处理器"""
    
    def __init__(self, task_queue: TaskQueue):
        super().__init__([
            EventType.DOCUMENT_UPLOADED,
            EventType.DOCUMENT_PARSED,
            EventType.DOCUMENT_CHUNKED,
            EventType.DOCUMENT_VECTORIZED
        ])
        self.task_queue = task_queue
    
    async def handle(self, event: Event) -> bool:
        """处理文档流水线事件"""
        try:
            if event.metadata.event_type == EventType.DOCUMENT_UPLOADED:
                # 创建解析任务
                parse_task = Task.create(
                    task_type="document.parse",
                    payload={
                        "document_id": event.payload["document_id"],
                        "file_path": event.payload["file_path"],
                        "user_id": event.payload.get("user_id")
                    },
                    priority=TaskPriority.HIGH,
                    correlation_id=event.metadata.correlation_id
                )
                await self.task_queue.enqueue(parse_task)
                
            elif event.metadata.event_type == EventType.DOCUMENT_PARSED:
                # 创建分块任务
                chunk_task = Task.create(
                    task_type="document.chunk",
                    payload={
                        "document_id": event.payload["document_id"],
                        "content": event.payload["content"],
                        "user_id": event.payload.get("user_id")
                    },
                    priority=TaskPriority.HIGH,
                    correlation_id=event.metadata.correlation_id
                )
                await self.task_queue.enqueue(chunk_task)
                
            elif event.metadata.event_type == EventType.DOCUMENT_CHUNKED:
                # 创建向量化任务
                vectorize_task = Task.create(
                    task_type="document.vectorize",
                    payload={
                        "document_id": event.payload["document_id"],
                        "chunks": event.payload["chunks"],
                        "user_id": event.payload.get("user_id")
                    },
                    priority=TaskPriority.HIGH,
                    correlation_id=event.metadata.correlation_id
                )
                await self.task_queue.enqueue(vectorize_task)
                
            elif event.metadata.event_type == EventType.DOCUMENT_VECTORIZED:
                # 创建索引任务
                index_task = Task.create(
                    task_type="document.index",
                    payload={
                        "document_id": event.payload["document_id"],
                        "chunks": event.payload["chunks"],
                        "embeddings": event.payload["embeddings"],
                        "user_id": event.payload.get("user_id")
                    },
                    priority=TaskPriority.HIGH,
                    correlation_id=event.metadata.correlation_id
                )
                await self.task_queue.enqueue(index_task)
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling document pipeline event: {e}")
            return False


async def setup_document_pipeline(event_bus: EventBus, task_queue: TaskQueue):
    """设置文档处理流水线"""
    
    # 注册任务处理器
    task_queue.register_processor(DocumentUploadProcessor())
    task_queue.register_processor(DocumentParseProcessor())
    task_queue.register_processor(DocumentChunkProcessor())
    task_queue.register_processor(DocumentVectorizeProcessor())
    task_queue.register_processor(DocumentIndexProcessor())
    
    # 注册事件处理器
    pipeline_handler = DocumentPipelineEventHandler(task_queue)
    event_bus.register_handler(pipeline_handler)
    
    logger.info("Document processing pipeline setup completed")


async def start_document_processing(
    document_id: str,
    file_path: str,
    user_id: Optional[str] = None,
    topic_id: Optional[str] = None
) -> str:
    """启动文档处理流程"""
    
    # 创建上传任务
    upload_task = Task.create(
        task_type="document.upload",
        payload={
            "document_id": document_id,
            "file_path": file_path,
            "user_id": user_id,
            "topic_id": topic_id
        },
        priority=TaskPriority.HIGH
    )
    
    # 加入任务队列
    task_queue = get_task_queue()
    task_id = await task_queue.enqueue(upload_task)
    
    logger.info(f"Started document processing for {document_id}, task_id: {task_id}")
    return task_id


# 导入全局实例
from .task_queue import get_task_queue
