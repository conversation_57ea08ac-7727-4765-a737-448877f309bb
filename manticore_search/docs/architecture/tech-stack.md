# 技术栈详情

## 技术选型原则

### 选型标准
1. **成熟稳定**: 选择经过生产验证的技术
2. **社区活跃**: 有活跃的开源社区支持
3. **文档完善**: 有完整的官方文档和教程
4. **性能优异**: 满足高性能要求
5. **易于维护**: 降低长期维护成本

### 架构约束
- **Python 生态**: 基于 Python 3.11 构建
- **异步优先**: 支持高并发异步处理
- **类型安全**: 全面使用类型注解
- **容器化**: 支持 Docker 容器化部署

## 核心技术栈

### Web 框架层

#### FastAPI 0.104.1
**选择理由**:
- 🚀 **高性能**: 基于 Starlette 和 Pydantic，性能接近 NodeJS 和 Go
- 📝 **自动文档**: 自动生成 OpenAPI/Swagger 文档
- 🔒 **类型安全**: 基于 Python 类型注解的数据验证
- ⚡ **异步支持**: 原生支持 async/await
- 🔧 **依赖注入**: 强大的依赖注入系统

**核心特性**:
```python
from fastapi import FastAPI, Depends
from pydantic import BaseModel

app = FastAPI(
    title="Manticore Search API",
    description="高性能搜索引擎接口",
    version="1.0.0"
)

class DocumentCreate(BaseModel):
    title: str
    content: str

@app.post("/documents")
async def create_document(doc: DocumentCreate):
    return {"message": "文档创建成功"}
```

**优势**:
- 自动请求/响应验证
- 内置安全功能
- 高性能异步处理
- 丰富的生态系统

#### Uvicorn (ASGI 服务器)
**选择理由**:
- ⚡ **高性能**: 基于 uvloop 的异步服务器
- 🔄 **热重载**: 开发环境自动重载
- 📊 **监控支持**: 内置性能监控
- 🐳 **容器友好**: 适合容器化部署

### 数据验证层

#### Pydantic 2.5.0
**选择理由**:
- 🔒 **类型安全**: 基于 Python 类型注解
- ⚡ **高性能**: 使用 Rust 编写的核心验证器
- 🔧 **灵活配置**: 丰富的验证和序列化选项
- 📝 **自动文档**: 与 FastAPI 完美集成

**核心特性**:
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List

class DocumentModel(BaseModel):
    title: str = Field(..., max_length=500, description="文档标题")
    content: str = Field(..., max_length=50000, description="文档内容")
    embedding: Optional[List[float]] = Field(None, description="向量数据")
    
    @validator('embedding')
    def validate_embedding_dimensions(cls, v):
        if v:
            # 使用配置的向量维度而非硬编码
            from manticore_search.utils.config import get_settings
            settings = get_settings()
            expected_dims = settings.vector_dimensions
            if len(v) != expected_dims:
                raise ValueError(f'向量维度必须为 {expected_dims}')
        return v
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
```

#### Pydantic Settings
**选择理由**:
- 🔧 **配置管理**: 统一的配置管理方案
- 🌍 **环境变量**: 自动读取环境变量
- 🔒 **类型验证**: 配置项类型安全
- 📝 **文档生成**: 自动生成配置文档

### 数据库层

#### Manticore Search 6.0+
**选择理由**:
- 🔍 **全文搜索**: 强大的全文搜索能力
- 🧠 **向量搜索**: 支持 KNN 向量相似度搜索
- ⚡ **高性能**: C++ 编写，性能优异
- 🔄 **实时索引**: 支持实时数据更新
- 🛠️ **SQL 兼容**: 支持标准 SQL 语法

**核心特性**:
```sql
-- 创建支持向量搜索的表
CREATE TABLE documents (
    id BIGINT,
    title TEXT INDEXED STORED,
    content TEXT INDEXED STORED,
    embedding FLOAT_VECTOR knn_type='hnsw' knn_dims='128' hnsw_similarity='cosine'
) engine='columnar';

-- 全文搜索
SELECT *, WEIGHT() as score FROM documents 
WHERE MATCH('Python 编程') 
ORDER BY score DESC LIMIT 10;

-- 向量搜索
SELECT *, KNN_DIST() as score FROM documents 
WHERE KNN('embedding', (0.1,0.2,0.3,...), 10);
```

#### PyMySQL 1.1.0
**选择理由**:
- 🐍 **纯 Python**: 无需编译，易于部署
- 🔒 **安全性**: 支持参数化查询，防止 SQL 注入
- 🔄 **连接池**: 支持连接池管理
- 📊 **监控友好**: 丰富的连接状态信息

**核心特性**:
```python
import pymysql

# 连接配置
connection = pymysql.connect(
    host='localhost',
    port=9306,
    user='',
    password='',
    charset='utf8mb4',
    cursorclass=pymysql.cursors.DictCursor
)

# 参数化查询
with connection.cursor() as cursor:
    sql = "SELECT * FROM documents WHERE title LIKE %s"
    cursor.execute(sql, ('%Python%',))
    results = cursor.fetchall()
```

## 开发工具栈

### 测试框架

#### pytest 7.4.3
**选择理由**:
- 🧪 **简单易用**: 简洁的测试语法
- 🔧 **插件丰富**: 大量的插件生态
- 📊 **覆盖率**: 内置代码覆盖率支持
- ⚡ **并行执行**: 支持并行测试

**核心特性**:
```python
import pytest
from manticore_search import DocumentService

class TestDocumentService:
    @pytest.fixture
    def service(self):
        return DocumentService(get_settings())
    
    def test_create_document(self, service):
        doc = service.create_document(DocumentCreate(
            title="测试", content="内容"
        ))
        assert doc.id is not None
        
    @pytest.mark.asyncio
    async def test_async_operation(self):
        result = await some_async_function()
        assert result is not None
```

#### pytest-asyncio 0.21.1
**选择理由**:
- ⚡ **异步测试**: 支持 async/await 测试
- 🔧 **Fixture 支持**: 异步 fixture 支持
- 📊 **事件循环**: 自动管理事件循环

#### httpx 0.25.2
**选择理由**:
- 🌐 **HTTP 客户端**: 现代的 HTTP 客户端库
- ⚡ **异步支持**: 支持异步 HTTP 请求
- 🧪 **测试友好**: 与 FastAPI TestClient 集成

### 代码质量工具

#### Black 23.11.0
**选择理由**:
- 🎨 **代码格式化**: 统一的代码风格
- ⚡ **快速**: 高性能格式化
- 🔧 **零配置**: 开箱即用
- 🤝 **团队协作**: 消除代码风格争议

**配置示例**:
```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.venv
  | build
  | dist
)/
'''
```

#### isort 5.12.0
**选择理由**:
- 📦 **导入排序**: 自动排序和分组导入
- 🔧 **可配置**: 丰富的配置选项
- 🤝 **Black 兼容**: 与 Black 完美配合

#### flake8 6.1.0
**选择理由**:
- 🔍 **代码检查**: 静态代码分析
- 📏 **PEP 8**: 强制 PEP 8 编码规范
- 🔧 **插件系统**: 丰富的插件生态

### 系统监控

#### psutil 5.9.6
**选择理由**:
- 📊 **系统监控**: 跨平台系统资源监控
- 🔧 **进程管理**: 进程信息获取
- 📈 **性能指标**: CPU、内存、磁盘监控

**核心特性**:
```python
import psutil

# 获取系统信息
cpu_percent = psutil.cpu_percent(interval=1)
memory = psutil.virtual_memory()
disk = psutil.disk_usage('/')

# 进程信息
process = psutil.Process()
print(f"内存使用: {process.memory_info().rss / 1024 / 1024:.1f} MB")
```

#### NumPy 1.24.3
**选择理由**:
- 🧮 **数值计算**: 高性能数值计算库
- 🧠 **向量操作**: 向量和矩阵运算
- 📊 **数据处理**: 大规模数据处理

## 部署技术栈

### 容器化

#### Docker
**选择理由**:
- 📦 **容器化**: 应用容器化部署
- 🔄 **一致性**: 开发、测试、生产环境一致
- 🚀 **快速部署**: 快速启动和扩展
- 🌍 **跨平台**: 支持多种操作系统

**Dockerfile 示例**:
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements-manticore.txt .
RUN pip install -r requirements-manticore.txt

COPY manticore_search/ ./manticore_search/
COPY run_manticore_api.py .

EXPOSE 8000

CMD ["python", "run_manticore_api.py", "--host", "0.0.0.0"]
```

#### Docker Compose
**选择理由**:
- 🔧 **多容器编排**: 管理多个相关容器
- 🌐 **网络管理**: 自动容器网络配置
- 📊 **服务依赖**: 定义服务启动顺序

**docker-compose.yml 示例**:
```yaml
version: '3.8'

services:
  manticore:
    image: manticoresearch/manticore:latest
    ports:
      - "9306:9306"
      - "9308:9308"
    volumes:
      - manticore_data:/var/lib/manticore
    
  api:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - manticore
    environment:
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306

volumes:
  manticore_data:
```

## 技术决策记录

### 为什么选择 FastAPI 而不是 Flask？
1. **性能**: FastAPI 性能更优，支持异步
2. **类型安全**: 基于 Pydantic 的自动验证
3. **文档**: 自动生成 API 文档
4. **现代化**: 支持最新的 Python 特性

### 为什么选择 Manticore Search 而不是 Elasticsearch？
1. **性能**: Manticore 在全文搜索方面性能更优
2. **向量搜索**: 原生支持向量搜索
3. **资源占用**: 更低的内存和 CPU 占用
4. **SQL 兼容**: 支持标准 SQL 语法

### 为什么选择 PyMySQL 而不是 aiomysql？
1. **简单性**: PyMySQL 更简单，易于调试
2. **稳定性**: 更成熟稳定的库
3. **兼容性**: 更好的 Manticore Search 兼容性
4. **未来扩展**: 可以轻松迁移到异步版本

## 性能基准

### 框架性能对比
| 框架 | 请求/秒 | 平均延迟 | 内存使用 |
|------|---------|----------|----------|
| FastAPI | 20,000+ | 5ms | 50MB |
| Flask | 5,000 | 20ms | 30MB |
| Django | 3,000 | 30ms | 80MB |

### 搜索性能指标
| 操作类型 | 响应时间 | QPS | 内存使用 |
|----------|----------|-----|----------|
| 全文搜索 | < 50ms | 1000+ | 100MB |
| 向量搜索 | < 100ms | 500+ | 200MB |
| 混合搜索 | < 150ms | 300+ | 250MB |

## 依赖管理

### 生产依赖
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
pymysql==1.1.0
numpy==1.24.3
psutil==5.9.6
python-multipart==0.0.6
```

### 开发依赖
```txt
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.0
```

### 版本兼容性
- **Python**: 3.9, 3.10, 3.11, 3.12
- **Manticore Search**: 6.0+
- **Docker**: 20.0+
- **操作系统**: Linux, macOS, Windows

## 未来技术规划

### 短期计划 (1-3 月)
- 🔒 **认证系统**: 集成 JWT 认证
- 📊 **监控系统**: Prometheus + Grafana
- 🚀 **缓存层**: Redis 缓存支持
- 🔄 **异步优化**: 全面异步化改造

### 长期规划 (3-12 月)
- 🌐 **微服务**: 拆分为多个微服务
- 📈 **分布式**: 支持分布式部署
- 🤖 **AI 集成**: 集成机器学习模型
- 🔍 **高级搜索**: 支持更多搜索算法

## 变更日志

| 日期 | 版本 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 2025-08-13 | 1.0.0 | 初始技术栈文档创建 | Winston (Architect) |
