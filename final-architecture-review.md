# 架构决策与行动计划 (v2)

**版本说明**: 本文档是在初步审查报告的基础上，经过深入讨论和 `deepwiki` 工具辅助调研后，形成的第二版架构决策文档。它将取代旧版报告，作为后续开发和运维的核心指导。

本文档分为两部分：
1.  **已确认的架构决策与行动计划**：记录了我们已达成共识的决策，并提供了具体的实施方案。
2.  **待深入探讨的架构议题**：列出了我们尚未充分讨论，需要后续跟进的问题。

---

## 第一部分：已确认的架构决策与行动计划

### 1. 核心技术栈总览

- **后端框架**: FastAPI (Python 3.11+)
- **搜索与向量**: Manticore Search (短期内)
- **关系型数据库**: PostgreSQL
- **缓存**: Redis
- **后台任务队列**: Dramatiq
- **开发环境**: Docker Compose
- **生产部署 (初期)**: 托管式容器平台 (如 Google Cloud Run, AWS ECS/App Runner)

### 2. 后端异步策略 (Async Strategy)

**决策**: 为保证系统高性能和高吞吐，所有服务必须遵循“异步优先”原则。任何可能导致事件循环阻塞的 I/O 操作，都必须采用异步方式处理。

**行动计划**:

- **数据库驱动**:
    - **Manticore Search**:
        - **决策**: 放弃使用同步的 `pymysql` 驱动。
        - **行动**: 引入并使用官方推荐的 `manticoresearch-python-asyncio` 库，对现有 `manticore_client.py` 进行重构，提供 `async` 接口。
    - **PostgreSQL**:
        - **决策**: 放弃使用同步的 `psycopg2-binary` 驱动。
        - **行动**: 引入并使用 `asyncpg` 库，配合 `SQLAlchemy 2.0+` 的异步支持进行数据库操作。
- **后台任务队列**:
    - **决策**: 放弃使用 Celery。
    - **行动**: 引入 `Dramatiq` 并配置其 `AsyncIO` 中间件。所有后台任务（如文档处理、数据同步等）都应定义为 `async def` 的 `dramatiq` actor，确保任务的调度和执行与 FastAPI 的事件循环兼容。

### 3. Embedding 向量化策略

**决策**: 为了降低运维复杂度和资源成本，生产环境中的向量化（Embedding）将采用“服务化”模式。

**行动计划**:

- **开发/测试环境/生产环境**:
    - **首选方案**: 采用第三方云 Embedding API (如 OpenAI兼容服务)。
        - **行动**: 在 `embedding_service` 中增加一个适配层，允许通过配置切换不同的 Embedding 服务提供商。评估不同服务商的成本、延迟和隐私政策。
    - **备选方案**: 若对数据隐私或成本有更高要求，可再评估方案，如 huggingface 开源模型
    `embedding_service` 通过内部 API 调用该服务。

### 4. 生产部署策略 (Production Deployment)

**决策**: 项目初期采用“托管式容器平台”进行生产部署，以实现快速上线和低运维成本。

**行动计划**:

- **部署平台**:
    - **决策**: 选择一个主流的托管式容器平台，如 Google Cloud Run 或 AWS ECS/App Runner。
    - **行动**: 编写对应平台的部署配置文件（如 `service.yaml` for Cloud Run）。
- **有状态服务 (Stateful Services) 的部署**:
    - **决策**: 对 Manticore 和 PostgreSQL 等有状态服务，采用“存算分离”模式进行部署。
    - **行动**:
        1.  **创建持久化存储**: 在所选的云平台上，为 Manticore 和 PostgreSQL 分别创建独立的持久化网络硬盘 (如 Google Persistent Disk, AWS EBS)。
        2.  **挂载卷**: 在托管服务的部署配置中，将创建好的网络硬盘挂载到 Manticore 和 PostgreSQL 容器的指定数据目录 (e.g., `/var/lib/manticore`, `/var/lib/postgresql/data`)。
        3.  **配置备份**: 设置自动化备份策略，定期将持久化硬盘的数据快照或逻辑备份（使用 `manticore-backup`, `pg_dump`）存储到成本更低的云对象存储 (如 Google Cloud Storage, AWS S3) 中。

---

## 第二部分：已确认的架构决策与行动计划 (续)

以下部分记录了原“待深入探讨议题”的最终决策。

### 5. 可观测性体系 (Observability)

**决策**: 采用分阶段策略。项目初期以 **Sentry** 为核心，实现快速、轻量级的监控；未来根据业务复杂度，再引入 **OpenTelemetry + Prometheus** 进行深度定制。

**行动计划**:
- **第一阶段 (项目初期)**:
    - **核心工具**: **Sentry**。
    - **实施**:
        1.  在应用中集成 Sentry SDK，并配置 `SENTRY_DSN`。
        2.  开启 Sentry 的**性能监控 (APM)** 与**分布式追踪 (Tracing)** 功能。
        3.  依赖 Sentry 进行核心的错误追踪和性能分析。
    - **日志**: 依赖 Traefik 的访问日志和应用自身的控制台日志，由容器平台统一收集。
- **第二阶段 (成熟期)**:
    - **触发条件**: 当 Sentry 的性能分析或指标无法满足精细化运营和排障需求时。
    - **措施**:
        1.  引入 **OpenTelemetry** 作为统一的埋点和数据采集标准。
        2.  引入 **Prometheus** 进行指标收集，**Grafana** 进行可视化，实现高度定制化的监控仪表盘。
        3.  将结构化日志 (如 `structlog`) 通过 OpenTelemetry 对接到统一的日志后端。

### 6. 安全与配置管理 (Security & Configuration)

**决策**: 生产环境严禁使用 `.env` 文件管理敏感信息，采用专业的密钥管理服务。

**行动计划**:
- **密钥管理**:
    - **短期方案**: 为实现快速部署和低运维成本，初期采用所选云平台自带的密钥管理服务 (如 **Google Secret Manager** 或 **AWS Secrets Manager**)。
    - **长期演进**: 在文档中明确，当项目扩展至多云/混合云，或需要动态密钥等高级功能时，将启动对 **HashiCorp Vault** 的评估和迁移计划。
- **网络安全**: 遵循云厂商的最佳实践，使用 VPC、子网和防火墙规则进行网络隔离和访问控制。
- **身份认证**: JWT 密钥等敏感信息必须存储在所选的密钥管理服务中。

### 7. CI/CD (持续集成/持续部署)

**决策**: 选择与代码仓库紧密集成的 **GitHub Actions** 作为 CI/CD 工具，建立自动化、可靠的部署流水线。

**行动计划**:
- **流水线设计**:
    - **CI (持续集成)**: 在代码 `push` 和 `pull_request` 到 `main` 分支时触发。流程包括：**代码质量与安全扫描 (CodeQL)** -> **自动化测试 (pytest)** -> **构建并推送 Docker 镜像**到容器仓库 (如 GHCR)。
    - **CD (持续部署)**:
        - **预发布环境 (Staging)**: 当代码合并到 `staging` 分支时，自动部署到预发布环境。
        - **生产环境 (Production)**: **通过在 GitHub 上创建新的 Release Tag (e.g., `v1.0.0`) 手动触发**，以保证生产部署的严肃性和可控性。
- **部署策略**:
    - **方案**: 采用**蓝绿部署 (Blue-Green Deployment)**。
    - **实施**: 利用云平台 (如 Google Cloud Run) 的流量管理功能，实现新旧版本的平滑切换和快速回滚。

### 8. 搜索与向量层的长期演进

**决策**: 采用分阶段的演进策略，优先挖掘并扩展 Manticore Search 自身潜力，避免过早引入新技术栈。

**行动计划**:
- **第一阶段 (垂直扩展与优化)**:
    - **触发条件**: 向量数量 > 1000万，或单节点资源利用率持续 > 75%。
    - **措施**: 启用 Manticore 的**列式存储 (Columnar Storage)** 优化内存；精细化调优 `searchd` 核心参数。
- **第二阶段 (Manticore 水平扩展)**:
    - **触发条件**: 垂直扩展后性能仍不满足要求，或数据量预估短期内 > 5000万。
    - **措施**: 搭建 Manticore 集群，实施**分库 (Distributed Tables)** 与**复制 (Replication)**。
- **第三阶段 (替代方案评估)**:
    - **触发条件**: Manticore 集群规模变得庞大 (如 > 10节点)，或在特定场景下性能遇瓶颈，且向量数 > 1亿。
    - **措施**: 启动对 **Milvus, Weaviate** 等专用向量数据库的 POC，重点评估其**性能、成本**及**迁移复杂度**。