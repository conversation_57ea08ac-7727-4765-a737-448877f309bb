"""
Manticore Search 客户端

提供与 Manticore Search 数据库的底层连接和操作
"""

import pymysql
import time
from typing import Optional, List, Dict, Any, Tuple, Union
from contextlib import contextmanager
from ..utils import (
    get_client_logger, 
    Settings, 
    ConnectionError, 
    QueryError, 
    TimeoutError,
    handle_pymysql_error
)


class ManticoreClient:
    """Manticore Search 客户端"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = get_client_logger()
        self._connection_pool = []
        self._pool_size = settings.connection_pool_size
        
    def _create_connection(self) -> pymysql.Connection:
        """创建数据库连接"""
        try:
            connection = pymysql.connect(
                host=self.settings.manticore_host,
                port=self.settings.manticore_port,
                user=self.settings.manticore_user,
                password=self.settings.manticore_password,
                charset=self.settings.manticore_charset,
                autocommit=True,
                connect_timeout=self.settings.connection_timeout,
                read_timeout=self.settings.read_timeout,
                cursorclass=pymysql.cursors.DictCursor
            )
            self.logger.debug(f"创建连接成功: {self.settings.manticore_host}:{self.settings.manticore_port}")
            return connection
        except Exception as e:
            self.logger.error(f"创建连接失败: {e}")
            raise handle_pymysql_error(e)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        connection = None
        try:
            connection = self._create_connection()
            yield connection
        except Exception as e:
            self.logger.error(f"连接操作失败: {e}")
            raise handle_pymysql_error(e)
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    self.logger.warning(f"关闭连接时出错: {e}")
    
    def execute_query(
        self, 
        sql: str, 
        params: Optional[Tuple] = None,
        fetch_results: bool = True
    ) -> Optional[List[Dict[str, Any]]]:
        """执行 SQL 查询"""
        start_time = time.time()
        
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    self.logger.debug(f"执行 SQL: {sql}")
                    if params:
                        self.logger.debug(f"参数: {params}")
                    
                    cursor.execute(sql, params)
                    
                    if fetch_results:
                        results = cursor.fetchall()
                        execution_time = (time.time() - start_time) * 1000
                        self.logger.debug(f"查询完成，返回 {len(results)} 条记录，耗时 {execution_time:.2f}ms")
                        return results
                    else:
                        execution_time = (time.time() - start_time) * 1000
                        self.logger.debug(f"执行完成，耗时 {execution_time:.2f}ms")
                        return None
                        
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"查询失败，耗时 {execution_time:.2f}ms: {e}")
            raise handle_pymysql_error(e)
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("SHOW TABLES")
                    self.logger.info("数据库连接测试成功")
                    return True
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        try:
            results = self.execute_query("SHOW VERSION")
            if results:
                return {
                    "version": results[0].get("Value", "Unknown"),
                    "status": "connected"
                }
            return {"status": "connected", "version": "Unknown"}
        except Exception as e:
            self.logger.error(f"获取服务器信息失败: {e}")
            return {"status": "error", "error": str(e)}
    
    def create_table(self, table_name: str, schema: str) -> bool:
        """创建表"""
        try:
            # 先删除已存在的表
            drop_sql = f"DROP TABLE IF EXISTS {table_name}"
            self.execute_query(drop_sql, fetch_results=False)
            
            # 创建新表
            create_sql = f"CREATE TABLE {table_name} ({schema}) engine='columnar'"
            self.execute_query(create_sql, fetch_results=False)
            
            self.logger.info(f"表 '{table_name}' 创建成功")
            return True
        except Exception as e:
            self.logger.error(f"创建表 '{table_name}' 失败: {e}")
            raise
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            results = self.execute_query("SHOW TABLES")
            if results:
                table_names = [row.get('Index', '') for row in results]
                return table_name in table_names
            return False
        except Exception as e:
            self.logger.error(f"检查表 '{table_name}' 是否存在失败: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            # 获取表结构
            describe_sql = f"DESCRIBE {table_name}"
            structure = self.execute_query(describe_sql)
            
            # 获取表状态
            status_sql = f"SHOW TABLE {table_name} STATUS"
            status = self.execute_query(status_sql)
            
            return {
                "structure": structure or [],
                "status": status[0] if status else {},
                "exists": True
            }
        except Exception as e:
            self.logger.error(f"获取表 '{table_name}' 信息失败: {e}")
            return {"exists": False, "error": str(e)}
    
    def insert_document(
        self,
        table_name: str,
        document: Dict[str, Any]
    ) -> bool:
        """插入单个文档
        
        改进：对于已经格式化为 Manticore float_vector 字面量的 embedding 字段（如 "(0.1,0.2,...)"
        ）不使用参数化占位符以避免被 pymysql 当作普通字符串（text）插入并保留 float_vector 类型。
        其他字段仍然使用参数化以防注入。
        """
        try:
            # 处理向量字段格式
            processed_doc = self._process_document_for_insert(document)
 
            # 为 embedding 字段准备特殊处理：如果是已经格式化的字面量字符串 '(...)'，则直接放到 SQL 中
            fields = []
            values = []
            literal_fields = {}  # field -> literal SQL fragment (no quoting)
 
            for k, v in processed_doc.items():
                if k == 'embedding' and isinstance(v, str) and v.startswith('(') and v.endswith(')'):
                    # embedding 已为 Manticore 字面量，作为 SQL 片段直接使用
                    fields.append(k)
                    literal_fields[k] = v
                else:
                    fields.append(k)
                    values.append(v)
 
            # 构建 SQL：将 literal fields 放在 VALUES 中的对应位置，而其他字段使用 %s 占位
            placeholders = []
            val_iter = iter(values)
            for f in fields:
                if f in literal_fields:
                    placeholders.append(literal_fields[f])
                else:
                    placeholders.append('%s')
 
            sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
            params = tuple(values)
 
            self.execute_query(sql, params, fetch_results=False)
            self.logger.debug(f"文档插入成功到表 '{table_name}'")
            return True
 
        except Exception as e:
            self.logger.error(f"插入文档到表 '{table_name}' 失败: {e}")
            raise
    
    def bulk_insert_documents(
        self, 
        table_name: str, 
        documents: List[Dict[str, Any]]
    ) -> int:
        """批量插入文档"""
        if not documents:
            return 0
            
        success_count = 0
        
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    for doc in documents:
                        try:
                            # 处理向量字段格式
                            processed_doc = self._process_document_for_insert(doc)

                            fields = list(processed_doc.keys())
                            placeholders = ', '.join(['%s'] * len(fields))
                            sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({placeholders})"
                            values = tuple(processed_doc.values())

                            cursor.execute(sql, values)
                            success_count += 1

                        except Exception as e:
                            self.logger.warning(f"批量插入中单个文档失败: {e}")
                            continue
            
            self.logger.info(f"批量插入完成: {success_count}/{len(documents)} 成功")
            return success_count
            
        except Exception as e:
            self.logger.error(f"批量插入失败: {e}")
            raise handle_pymysql_error(e)
    
    def search_documents(
        self, 
        table_name: str, 
        query: str, 
        limit: int = 20,
        offset: int = 0,
        additional_conditions: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            # 构建搜索 SQL
            sql = f"""
            SELECT *, WEIGHT() as relevance_score 
            FROM {table_name} 
            WHERE MATCH(%s)
            """
            
            params = [query]
            
            # 添加额外条件
            if additional_conditions:
                sql += f" AND {additional_conditions}"
            
            # 添加排序和分页
            sql += " ORDER BY relevance_score DESC"
            sql += f" LIMIT {offset}, {limit}"
            
            results = self.execute_query(sql, tuple(params))
            return results or []
            
        except Exception as e:
            self.logger.error(f"搜索文档失败: {e}")
            raise handle_pymysql_error(e)
    
    def _process_document_for_insert(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理文档数据以适配Manticore插入格式
        特别处理向量字段，确保正确的float_vector格式
        """
        processed_doc = document.copy()

        # 处理embedding字段
        if 'embedding' in processed_doc and processed_doc['embedding'] is not None:
            embedding = processed_doc['embedding']

            # 如果是列表格式，转换为Manticore float_vector格式
            if isinstance(embedding, list):
                # 转换为括号包围的逗号分隔格式: (0.1,0.2,0.3,...)
                vector_str = '(' + ','.join(map(str, embedding)) + ')'
                processed_doc['embedding'] = vector_str
            elif isinstance(embedding, str) and not embedding.startswith('('):
                # 如果是字符串但不是正确格式，尝试解析并重新格式化
                try:
                    import ast
                    vector_list = ast.literal_eval(embedding)
                    if isinstance(vector_list, list):
                        vector_str = '(' + ','.join(map(str, vector_list)) + ')'
                        processed_doc['embedding'] = vector_str
                except:
                    self.logger.warning(f"无法解析向量字符串格式: {embedding[:50]}...")

        return processed_doc

    def close(self):
        """关闭客户端"""
        self.logger.info("Manticore 客户端关闭")


if __name__ == "__main__":
    # 测试客户端
    from ..utils import get_settings
    
    settings = get_settings()
    client = ManticoreClient(settings)
    
    # 测试连接
    if client.test_connection():
        print("✅ 连接测试成功")
        
        # 获取服务器信息
        info = client.get_server_info()
        print(f"服务器信息: {info}")
        
    else:
        print("❌ 连接测试失败")
