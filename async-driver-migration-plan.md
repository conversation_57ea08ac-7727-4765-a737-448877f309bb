异步策略与驱动迁移详细改动清单（草案）

目标
- 在保持现有功能的同时，消除主事件循环中可能的阻塞点，确保 FastAPI 服务在高并发时不被同步驱动、I/O 操作阻塞。
- 提供可逐步实施的改动清单、示例代码片段与 PR 草案说明，便于工程团队逐步落地并回滚。

范围与原则
- 优先保证线上可用性：采用渐进式改造 -> 包装同步调用（线程池）-> 逐步替换为异步驱动或独立同步 worker。
- 对外接口不变：HTTP API、MySQL 协议端口不变；内部实现逐步改造。
- 明确异步准则文档（附带 checklist），团队必须遵守。

总体方案（步骤）
1. 审计并定位所有同步外部 I/O 调用位置
   - 目标文件/位置优先级：数据库驱动、Manticore 客户端、模型推理调用、外部 HTTP 客户端
2. 引入通用封装层（sync_wrappers.py / sync_clients 模块）
   - 为每个同步客户端提供统一的 run_in_executor 封装与超时/隔离策略
3. 将 API 层调用替换为封装调用（最小改造）
   - 保持接口不变，内部使用线程池或独立 asyncio task 调用
4. 在中期逐个替换为异步驱动或单独微服务
   - Postgres -> asyncpg + SQLAlchemy async
   - Manticore -> 使用 HTTP JSON API 的 async HTTP 客户端或在独立同步 Worker 中使用 pymysql
   - Embedding 推理 -> 独立推理服务（gRPC/HTTP）异步调用
5. 清理与测试：单元/集成测试、负载测试、回滚方案

具体文件与改动建议（示例）
A. 位置扫描（必须检查）
- 全局文件（建议扫描）
  - [`requirements.txt`](requirements.txt:1)
  - 各服务目录下的 `services/`、`api/` 中的数据库/客户端使用处
- 重点示例文件（根据 workspace）
  - [`manticore_search/clients/manticore_client.py`](manticore_search/clients/manticore_client.py:1)
  - 应用层中调用数据库的模块：`user_service`, `document_service`, `conversation_service` 下的 `services` 目录内文件（逐个审查）

B. 通用封装模块（新增）
- 文件：[`shared/sync_client_wrapper.py`](shared/sync_client_wrapper.py:1)
- 功能（示例 Python）：
  - 提供 run_in_thread(func, *args, timeout=None) 接口
  - 自动记录指标（duration、timeouts、exceptions）
  - 配置全局线程池（ThreadPoolExecutor）大小可通过环境变量调整
- 示例伪码：
  - def run_in_thread(func, *args, timeout=None):
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(global_executor, functools.partial(func, *args))

C. 针对 Manticore 客户端的改造（短期 patch）
- 文件：[`manticore_search/clients/manticore_client.py`](manticore_search/clients/manticore_client.py:1)
- 变更：
  1. 将直接使用 pymysql 调用封装到新模块 `sync_client_wrapper.run_in_thread(...)`
  2. 在调用位置保持原调用签名，但将函数改为 async 并 await run_in_thread(原同步实现)
- 示例改造片段（伪）：
  - 原：
    def query(sql): conn = pymysql.connect(...); return conn.execute(sql)
  - 新：
    async def query(sql):
        def _sync_query(sql): ... # 原实现
        return await run_in_thread(_sync_query, sql, timeout=2.0)

D. Postgres 驱动迁移路线
- 短期：保持 psycopg2 调用但通过 run_in_thread 封装以避免阻塞主线程
- 中期：替换为 asyncpg + SQLAlchemy async
  - 修改点：
    - 更新 `requirements.txt`：增加 `asyncpg`、使用 SQLAlchemy 的 async 依赖（注：评估 sqlalchemy 版本兼容）
    - 在 `user_service/utils/config.py` 中增加 async DB URL 变量示例
    - 在 ORM 层写一套 async session 工厂（示例文件：`shared/db_async.py`）
- 回退策略：保留旧的同步实现并通过环境变量切换（SYNC_DB=true）

E. Redis 与缓存层
- 推荐：使用 `aioredis`（已在依赖）并把所有 redis 调用改为 async（medium 優先级）
- 短期：确保使用 aioredis，若发现同步 redis 使用则封装至 run_in_thread

F. Embedding 服务调用
- 方案 A（短期快速）：将本地 embedding 模型调用封装到独立进程/服务（embedding_worker），API 通过 HTTP/gRPC 异步调用
- 方案 B（长期）：部署专门推理服务（BentoML/TorchServe/Triton）；embedding_service 改为异步 HTTP 客户端（httpx async）
- 改动文件：
  - `embedding_service/services/embedding_service.py` -> 改造为 async endpoints，内部通过 async HTTP 调用推理服务
  - docker-compose 增加 embedding-worker 或推理服务容器

G. Celery 与异步队列
- 评估：若团队继续用 Celery（已在 requirements），保留但明确边界：
  - Web/API 层只做轻量任务调度（发布任务）；具体耗时逻辑放在 worker
  - 对 Celery 使用 Redis broker（已存在）并确保任务幂等/监控
- 可替代：dramatiq 或 Redis Streams（若要更好地支持 asyncio）
- 变更点：
  - `requirements.txt` 注明 Celery 版本与 broker 配置示例
  - 提供 `docs/async/` 的运行与监控说明（flower/metrics）

H. 示例 PR 草案说明（模板）
- PR 标题：feat(async): wrap sync Manticore client calls in executor
- PR 描述要点：
  - 变更范围与目标（避免 FastAPI 事件循环被阻塞）
  - 新增文件：`shared/sync_client_wrapper.py`
  - 修改文件：`manticore_search/clients/manticore_client.py`（将 query 方法改为 async 并用 wrapper）
  - 测试：新增单元测试 `tests/test_manticore_client_async.py`（覆盖超时/异常路径）
  - 回退说明：如何通过 env var 切换到旧实现（例如 USE_SYNC_CLIENT=true）
- 检查清单（CI）
  - 单元测试通过
  - 集成测试中基础搜索流程通过（ubertest 的相关用例）
  - 增加 load test 脚本（simple locust 或 k6 场景）

I. 指标与监控埋点（与封装同时加入）
- 在 `shared/sync_client_wrapper.py` 内记录 Prometheus 指标：
  - manticore_query_duration_seconds (Histogram)
  - manticore_query_errors_total (Counter)
  - manticore_query_timeouts_total (Counter)
- 配置建议：
  - 每个服务暴露 /metrics（Prometheus scrape）
  - Docker Compose 与 k8s Deployment 示例加入 scrape 配置

J. 测试策略
- 单元测试：针对 wrapper 行为（timeout、exception、正确返回）
- 集成测试：在 dev 环境起一套 minimal stack（manticore+service）并运行 search 流程
- 负载测试：对改造前后进行对比（CI 或 staging），确保延迟曲线改善

更新 TODO 清单（将调用 update_todo_list）
- [ ] 在仓库添加 `shared/sync_client_wrapper.py` 并修改 Manticore client 为 async wrapper
- [ ] 在若干服务（document_service, user_service, conversation_service）中逐步替换同步外部调用为封装调用
- [ ] 增加单元与集成测试，运行 ubertests 相关场景
- [ ] 评估并计划 PostgreSQL 异步迁移任务（asyncpg + SQLAlchemy async）
- [ ] Embedding 推理服务化 POC（并行任务）

交付物（我将创建/修改的文件）
- 新增：[`shared/sync_client_wrapper.py`](shared/sync_client_wrapper.py:1)（封装实现）
- 修改：[`manticore_search/clients/manticore_client.py`](manticore_search/clients/manticore_client.py:1)（示例改造）
- 新增：`tests/test_manticore_client_async.py`（覆盖）
- 文档：`docs/async/migration_guide.md`（迁移步骤、ENV 变量、测试指南）
- PR 模板草案文本文件：`docs/async/pr_template_async_migration.md`

下一步（需要你确认）
- 我现在开始按上面清单生成初始改动文件草案（`shared/sync_client_wrapper.py`、manticore 客户端示例改造、PR 模板与测试 stub），并提交为仓库文件。确认后我会一步步写入并更新 TODO 状态。