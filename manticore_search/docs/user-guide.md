# Manticore Search 模块用户指南

## 快速开始

### 系统要求
- Python 3.11 或更高版本
- Docker 和 Docker Compose
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

### 安装步骤

#### 1. 获取项目
```bash
# 如果是从 Git 仓库获取
git clone <repository-url>
cd zhi-manticore2

# 或者直接下载项目文件
```

#### 2. 环境设置
```bash
# 使用自动化脚本（推荐）
chmod +x setup_manticore_env.sh
./setup_manticore_env.sh

# 或者手动设置
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements-manticore.txt
```

#### 3. 启动服务
```bash
# 启动 Manticore Search 数据库
docker-compose up -d

# 等待服务启动（约 10 秒）
sleep 10

# 测试模块功能
python test_manticore_module.py

# 启动 API 服务
python run_manticore_api.py --reload
```

#### 4. 验证安装
访问以下 URL 验证安装：
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health
- **根路径**: http://localhost:8000/

## 基本使用

### 通过 API 使用

#### 创建文档
```bash
curl -X POST http://localhost:8000/api/v1/documents \
  -H "Content-Type: application/json" \
  -d '{
    "title": "我的第一个文档",
    "content": "这是一个示例文档，用于演示 Manticore Search 的功能。",
    "category": "示例"
  }'
```

#### 搜索文档
```bash
curl -X POST http://localhost:8000/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "示例",
    "search_type": "fulltext",
    "limit": 10
  }'
```

#### 获取文档列表
```bash
curl "http://localhost:8000/api/v1/documents?limit=10&offset=0"
```

### 通过 Python 代码使用

#### 基本示例
```python
import requests

# API 基础 URL
BASE_URL = "http://localhost:8000/api/v1"

# 创建文档
def create_document(title, content, category=None):
    data = {
        "title": title,
        "content": content,
        "category": category
    }
    response = requests.post(f"{BASE_URL}/documents", json=data)
    return response.json()

# 搜索文档
def search_documents(query, limit=20):
    data = {
        "query": query,
        "search_type": "fulltext",
        "limit": limit
    }
    response = requests.post(f"{BASE_URL}/search", json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 创建文档
    result = create_document(
        title="Python 编程入门",
        content="Python 是一种简单易学的编程语言，适合初学者学习。",
        category="编程教程"
    )
    print(f"文档创建成功，ID: {result['data']['id']}")
    
    # 搜索文档
    results = search_documents("Python 编程")
    print(f"找到 {results['data']['total']} 个相关文档")
    
    for item in results['data']['results']:
        doc = item['document']
        print(f"- {doc['title']} (分数: {doc['score']:.2f})")
```

#### 高级搜索示例
```python
import requests
import numpy as np

BASE_URL = "http://localhost:8000/api/v1"

# 向量搜索示例
def vector_search(query_text, query_vector, limit=10):
    data = {
        "query": query_text,
        "search_type": "vector",
        "query_vector": query_vector,
        "limit": limit
    }
    response = requests.post(f"{BASE_URL}/search", json=data)
    return response.json()

# 混合搜索示例
def hybrid_search(query_text, query_vector, vector_weight=0.5, limit=10):
    data = {
        "query": query_text,
        "search_type": "hybrid",
        "query_vector": query_vector,
        "vector_weight": vector_weight,
        "limit": limit
    }
    response = requests.post(f"{BASE_URL}/search", json=data)
    return response.json()

# 生成示例向量（实际使用中应该使用真实的向量）
def generate_sample_vector(text, dimensions=128):
    # 这里使用简单的哈希方法生成向量，实际应用中应使用专业的向量化模型
    import hashlib
    hash_obj = hashlib.md5(text.encode())
    hash_bytes = hash_obj.digest()
    
    vector = []
    for i in range(dimensions):
        byte_idx = i % len(hash_bytes)
        value = hash_bytes[byte_idx] / 255.0 * 2 - 1  # 归一化到 [-1, 1]
        vector.append(value)
    
    return vector

# 使用示例
if __name__ == "__main__":
    query_text = "机器学习算法"
    query_vector = generate_sample_vector(query_text)
    
    # 向量搜索
    vector_results = vector_search(query_text, query_vector)
    print(f"向量搜索找到 {vector_results['data']['total']} 个结果")
    
    # 混合搜索
    hybrid_results = hybrid_search(query_text, query_vector, vector_weight=0.7)
    print(f"混合搜索找到 {hybrid_results['data']['total']} 个结果")
```

### 通过模块直接使用

#### 导入模块
```python
from manticore_search import (
    DocumentService,
    SearchService,
    HealthService,
    DocumentCreate,
    create_simple_search_request,
    get_settings
)

# 初始化服务
settings = get_settings()
document_service = DocumentService(settings)
search_service = SearchService(settings)
```

#### 文档管理
```python
# 创建文档
doc_data = DocumentCreate(
    title="模块使用示例",
    content="这是通过模块直接创建的文档",
    category="示例"
)

document = document_service.create_document(doc_data)
print(f"文档创建成功，ID: {document.id}")

# 获取文档
retrieved_doc = document_service.get_document(document.id)
print(f"获取文档: {retrieved_doc.title}")

# 更新文档
from manticore_search.models import DocumentUpdate
update_data = DocumentUpdate(title="更新后的标题")
updated_doc = document_service.update_document(document.id, update_data)
print(f"文档更新成功: {updated_doc.title}")

# 删除文档
success = document_service.delete_document(document.id)
print(f"文档删除: {'成功' if success else '失败'}")
```

#### 搜索功能
```python
# 全文搜索
search_request = create_simple_search_request("示例", limit=10)
search_response = search_service.search(search_request)

print(f"搜索结果: {search_response.total} 个文档")
print(f"搜索耗时: {search_response.took:.2f}ms")

for result in search_response.results:
    doc = result.document
    print(f"- {doc.title} (分数: {doc.score:.2f})")
    if doc.snippet:
        print(f"  摘要: {doc.snippet}")
```

## 配置说明

### 环境变量配置

创建 `.env` 文件来配置系统参数：

```bash
# Manticore Search 连接配置
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_USER=
MANTICORE_PASSWORD=
MANTICORE_CHARSET=utf8mb4

# 连接池配置
MANTICORE_CONNECTION_POOL_SIZE=10
MANTICORE_CONNECTION_TIMEOUT=30
MANTICORE_READ_TIMEOUT=30

# 搜索配置
MANTICORE_DEFAULT_SEARCH_LIMIT=20
MANTICORE_MAX_SEARCH_LIMIT=100
MANTICORE_SNIPPET_LENGTH=200
MANTICORE_SNIPPET_AROUND=5

# 向量搜索配置
MANTICORE_VECTOR_DIMENSIONS=128
MANTICORE_KNN_SEARCH_LIMIT=10

# 日志配置
MANTICORE_LOG_LEVEL=INFO

# API 配置
MANTICORE_API_TITLE="Manticore Search API"
MANTICORE_API_DESCRIPTION="基于 Manticore Search 的高内聚搜索引擎接口"
MANTICORE_API_VERSION="1.0.0"
MANTICORE_API_PREFIX="/api/v1"

# 健康检查配置
MANTICORE_HEALTH_CHECK_TIMEOUT=5

# 表配置
MANTICORE_DEFAULT_TABLE_NAME=knowledge_base
```

### 常用配置项说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| MANTICORE_HOST | localhost | Manticore Search 主机地址 |
| MANTICORE_PORT | 9306 | MySQL 协议端口 |
| MANTICORE_HTTP_PORT | 9308 | HTTP API 端口 |
| MANTICORE_DEFAULT_SEARCH_LIMIT | 20 | 默认搜索结果数量 |
| MANTICORE_MAX_SEARCH_LIMIT | 100 | 最大搜索结果数量 |
| MANTICORE_VECTOR_DIMENSIONS | 128 | 向量维度 |
| MANTICORE_LOG_LEVEL | INFO | 日志级别 |

## 功能特性

### 文档管理
- ✅ **CRUD 操作**: 创建、读取、更新、删除文档
- ✅ **批量操作**: 批量创建和更新文档
- ✅ **分类管理**: 支持文档分类
- ✅ **统计信息**: 文档数量和分类统计

### 搜索功能
- ✅ **全文搜索**: 基于关键词的全文搜索
- ✅ **向量搜索**: 基于向量相似度的语义搜索
- ✅ **混合搜索**: 结合全文和向量搜索
- ✅ **高亮摘要**: 搜索结果高亮显示
- ✅ **分页支持**: 支持搜索结果分页
- ✅ **过滤功能**: 按分类、时间等条件过滤

### 系统功能
- ✅ **健康检查**: 系统和服务健康状态监控
- ✅ **性能监控**: API 响应时间和系统资源监控
- ✅ **日志系统**: 结构化日志记录
- ✅ **错误处理**: 完整的异常处理体系

## 性能优化

### 搜索性能优化
1. **索引优化**: 合理设置字段索引
2. **查询优化**: 使用参数化查询
3. **结果限制**: 合理设置搜索结果数量
4. **缓存策略**: 缓存热门查询结果（未来版本）

### 系统性能优化
1. **连接池**: 使用数据库连接池
2. **异步处理**: 使用异步 API 处理
3. **资源监控**: 监控系统资源使用
4. **负载均衡**: 多实例部署（未来版本）

## 故障排除

### 常见问题

#### 1. 服务启动失败
**问题**: API 服务无法启动
**解决方案**:
```bash
# 检查端口是否被占用
lsof -i :8000

# 检查 Manticore Search 是否运行
docker-compose ps

# 查看详细错误日志
python run_manticore_api.py --log-level debug
```

#### 2. 数据库连接失败
**问题**: 无法连接到 Manticore Search
**解决方案**:
```bash
# 检查 Manticore Search 状态
docker-compose logs manticore

# 重启 Manticore Search
docker-compose restart manticore

# 测试连接
python -c "
from manticore_search import ManticoreClient, get_settings
client = ManticoreClient(get_settings())
print('连接状态:', client.test_connection())
"
```

#### 3. 搜索结果为空
**问题**: 搜索不到任何结果
**解决方案**:
```bash
# 检查是否有文档数据
curl http://localhost:8000/api/v1/stats

# 检查搜索语法
curl -X POST http://localhost:8000/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"query": "*", "limit": 5}'

# 查看搜索日志
python run_manticore_api.py --log-level debug
```

#### 4. 向量搜索失败
**问题**: 向量搜索返回错误
**解决方案**:
```bash
# 检查向量维度
python -c "
from manticore_search.utils import get_settings
settings = get_settings()
print('向量维度:', settings.vector_dimensions)
"

# 测试向量搜索
python test_vector_search.py
```

### 日志分析

#### 查看日志
```bash
# 实时查看 API 日志
python run_manticore_api.py --log-level debug

# 查看 Manticore Search 日志
docker-compose logs -f manticore

# 查看系统资源使用
python -c "
from manticore_search.services import HealthService
from manticore_search.utils import get_settings
import asyncio

async def check_health():
    service = HealthService(get_settings())
    health = await service.get_health_status()
    print('系统状态:', health.status)
    for name, status in health.services.items():
        print(f'{name}: {status.status}')

asyncio.run(check_health())
"
```

#### 日志级别说明
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息记录
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

## 最佳实践

### 开发建议
1. **使用虚拟环境**: 避免依赖冲突
2. **配置环境变量**: 不要硬编码配置
3. **编写测试**: 确保代码质量
4. **监控日志**: 及时发现问题
5. **定期备份**: 备份重要数据

### 生产部署建议
1. **使用 HTTPS**: 保证数据传输安全
2. **设置防火墙**: 限制访问端口
3. **监控系统**: 设置告警机制
4. **定期更新**: 保持依赖版本最新
5. **负载测试**: 验证系统性能

## 版本升级

### 升级步骤
1. **备份数据**: 备份现有数据
2. **停止服务**: 停止 API 服务
3. **更新代码**: 获取最新版本
4. **更新依赖**: 安装新的依赖
5. **迁移数据**: 执行数据迁移（如需要）
6. **测试功能**: 验证功能正常
7. **启动服务**: 重新启动服务

### 版本兼容性
- **向后兼容**: API 接口保持向后兼容
- **数据兼容**: 数据格式保持兼容
- **配置兼容**: 配置项保持兼容

## 获取帮助

### 文档资源
- **项目文档**: 查看 `docs/` 目录下的详细文档
- **API 文档**: 访问 http://localhost:8000/docs
- **代码示例**: 查看 `test_*.py` 文件中的示例

### 社区支持
- **问题反馈**: 通过 GitHub Issues 报告问题
- **功能建议**: 提交功能请求
- **代码贡献**: 参与项目开发

### 联系方式
- **技术支持**: 联系项目维护者
- **商业合作**: 商业使用咨询

## 变更日志

| 日期 | 版本 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 2025-08-13 | 1.0.0 | 初始用户指南创建 | Winston (Architect) |
