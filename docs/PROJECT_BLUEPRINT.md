# 项目蓝图：知深学习导师 (Phoenix)

**版本**: 1.1
**日期**: 2025-08-14
**负责人**: <PERSON> (Architect)

---

## 一、项目整体概述

### 1.1 项目愿景

“知深学习导师”是一个个人化AI学习伴行系统。其核心目标并非简单地“提供答案”，而是通过可追溯、可复盘的引导式对话，帮助用户将碎片化的信息内化为自己结构化的、可长期记忆的个人知识库。

### 1.2 核心问题与解决方案

- **问题**: 传统AI对话存在上下文窗口限制，导致学习过程碎片化，无法形成长期记忆。
- **解决方案**: 本项目通过构建一个强大的、基于混合搜索的智能上下文引擎，实现跨会话的长期记忆，让AI能够真正理解学习的连续性。

### 1.3 核心特色

- **🧠 智能上下文引擎**: 基于 Manticore Search 的全文与向量混合搜索，突破传统大语言模型的上下文窗口限制。
- **📚 主题式长期记忆**: 支持围绕特定“主题”进行跨越数天甚至数月的连续学习，AI能清晰记忆历史对话与用户进度。
- **📝 可交互的动态摘要**: 每次学习会话后，系统能自动生成结构化的学习笔记（摘要），用户点击摘要中的任意一点，都能立刻回溯到原始的对话上下文。
- **🔄 引导式学习对话**: AI的角色不再是问答机，而是学习导师。它通过提问、类比、苏格拉底式对话等方式，引导用户主动思考、深度探索。

## 二、功能列表 (Features)

### 核心功能

| 模块 | 功能点 | 描述 | 优先级 |
| :--- | :--- | :--- | :--- |
| **用户服务** | 用户认证与管理 | 提供基于JWT的用户注册、登录、会话管理。 | P0 |
| **文档服务** | 文档处理与存储 | 支持多种格式（PDF, MD）的文档上传、解析、切块和存储。 | P1 |
| **向量化服务** | 文本向量化 | 将处理后的文本块转换为向量表示，供搜索使用。支持可插拔的第三方Embedding服务。 | P0 |
| **上下文引擎** | 混合搜索 | 提供全文+向量的混合搜索能力，从知识库中检索最相关的上下文。 | P0 |
| **主题服务** | 学习主题管理 | 用户可以创建、命名和管理自己的学习主题，将对话和文档归类。 | P1 |
| **对话服务** | 对话流程管理 | 管理用户与AI之间的多轮对话，支持WebSocket进行实时通信。 | P2 |
| **AI集成服务** | 大语言模型集成 | 与多种LLM（OpenAI, Claude, 本地模型）进行交互，生成引导式回复。 | P1 |
| **摘要服务** | 动态学习摘要 | 在对话结束后，异步生成可交互的、结构化的学习摘要。 | P2 |

### 支撑功能

| 功能分类 | 功能点 | 描述 |
| :--- | :--- | :--- |
| **API网关** | BFF网关模式 | 采用 **Traefik + FastAPI** 的组合模式。Traefik作为边缘网关，负责SSL卸载和基础路由；FastAPI作为BFF (Backend for Frontend)，负责请求聚合、协议转换和统一的业务认证逻辑。 |
| **后台任务** | 异步处理 | 使用Dramatiq处理耗时任务，如文档解析、向量化、摘要生成等。 |
| **可观测性** | 监控与告警 | 集成Sentry进行错误追踪和性能监控，未来可扩展至Prometheus+Grafana。 |
| **CI/CD** | 自动化部署 | 使用GitHub Actions实现自动化测试、构建和蓝绿部署。 |

## 三、技术栈与核心决策

本项目的技术选型遵循“成熟稳定、异步优先、类型安全、容器化”的原则。核心技术栈与关键决策如下：

| 分类 | 技术/工具 | 版本/决策 | 选择理由 |
| :--- | :--- | :--- | :--- |
| **前端框架** | React | Vite | **决策**: 采用模板自带的 React + Vite 方案。**理由**: 最大化复用模板提供的CI/CD与客户端生成工作流，社区生态成熟，能快速启动开发。 |
| **后端框架** | FastAPI | 0.104+ | 高性能、原生异步支持、自动文档生成、强大的依赖注入系统。 |
| **ORM** | SQLModel | | **决策**: 统一采用 SQLModel。**理由**: API简洁，与Pydantic/FastAPI高度集成，开发效率高。同时保留在复杂场景下回退至原生SQLAlchemy的能力。 |
| **Web服务器** | Uvicorn | | 高性能ASGI服务器，与FastAPI无缝集成。 |
| **数据验证** | Pydantic | 2.5+ | 提供强大的类型安全保障，与FastAPI完美结合。 |
| **搜索与向量** | Manticore Search | 6.0+ | 高性能C++内核，同时支持全文索引和KNN向量搜索，资源占用相对较低。 |
| **关系型数据库** | PostgreSQL | 13+ | 成熟、稳定、功能强大，社区支持广泛。 |
| **缓存** | Redis | 6+ | 高性能内存数据库，用于会话缓存和Dramatiq的消息代理。 |
| **后台任务队列** | Dramatiq | | 轻量、可靠，原生支持asyncio，与FastAPI的事件循环兼容良好。 |
| **数据库驱动** | asyncpg, manticore-async | | 全面采用异步驱动，保证I/O操作不阻塞FastAPI的事件循环。 |
| **容器化** | Docker & Docker Compose | | 实现开发、测试、生产环境的一致性，简化部署与管理。 |
| **项目模板** | fastapi/full-stack-fastapi-template | | 提供生产就绪的最佳实践，包括CI/CD、用户认证、前后端分离架构等。 |

## 四、功能模块和交互关系

系统采用微服务架构，各服务职责单一、高内聚、低耦合。通过API网关对外提供统一接口。

```mermaid
graph TD
    subgraph "用户端 (Web UI)"
        A[React App]
    end

    subgraph "网关层"
        B[BFF Gateway <br> (Traefik + FastAPI)]
    end

    subgraph "无状态服务 (Stateless Services)"
        C[User Service <br> (FastAPI)]
        D[Topic Service <br> (FastAPI)]
        E[Document Service <br> (FastAPI)]
        F[Conversation Service <br> (FastAPI)]
        G[LLM Integration <br> (FastAPI)]
        H[Summary Service <br> (FastAPI)]
        I[Embedding Service <br> (FastAPI)]
    end

    subgraph "有状态服务 (Stateful Services)"
        J[PostgreSQL <br> (用户/主题/文档元数据)]
        K[Manticore Search <br> (全文+向量索引)]
        L[Redis <br> (缓存/会话代理)]
    end

    subgraph "后台任务 (Background Workers)"
        M[Dramatiq Workers]
    end

    A -- HTTPS --> B;

    B -- REST/WebSocket --> C;
    B -- REST --> D;
    B -- REST --> E;
    B -- REST --> F;

    F -- REST --> G;
    F -- REST --> K;
    F -- REST --> D;
    F -- Publishes Task --> L;

    E -- Publishes Task --> L;
    H -- Publishes Task --> L;

    M -- Consumes Task --> L;
    M -- REST --> I;
    M -- Writes to --> J;
    M -- Writes to --> K;
    M -- REST --> G;

    C -- CRUD --> J;
    D -- CRUD --> J;
    E -- CRUD --> J;
    H -- CRUD --> J;
```

**核心交互流程 (示例：一次学习对话)**

1.  **用户**通过 **React Web UI** 发起一个关于“Python深度学习”主题的对话。
2.  请求经 **API Gateway** 转发至 **Conversation Service**。
3.  **Conversation Service** 从 **Manticore Search** 检索与该主题相关的历史对话上下文。
4.  **Conversation Service** 将用户问题和上下文信息，通过 **LLM Integration** 服务发送给大语言模型。
5.  **LLM Integration** 返回AI的引导式回复。
6.  对话结束后，**Conversation Service** 向 **Redis (Dramatiq Broker)** 发布一个“生成摘要”的任务。
7.  **Dramatiq Worker** 接收任务，调用 **LLM Integration** 生成摘要，并将结果存入 **PostgreSQL**。

## 五、详细的实现计划

我们将基于 `fastapi/full-stack-fastapi-template` 模板，从一个全新的项目开始，分阶段完成开发、集成与部署。

### 阶段一：基础架构搭建 (Week 1-2)

| 任务 | 描述 | 产出物 |
| :--- | :--- | :--- |
| **1. 项目初始化** | 使用 `copier` 基于模板生成新项目仓库。 | 一个包含模板完整结构的新Git仓库。 |
| **2. 环境验证** | 在本地通过 `docker compose watch` 成功运行所有原生服务。 | 本地开发环境搭建成功，能访问后端API和前端页面。 |
| **3. 配置管理策略** | **决策**: 采用混合注入方案。开发环境使用`.env`，生产环境通过云平台注入环境变量。 | 在`docs/deployment/config-management.md`中记录详细步骤。 |
| **4. 集成Manticore** | 在 `docker-compose.yml` 中添加 Manticore 服务，并编写一个异步客户端。 | Manticore服务可被后端API成功连接和查询。 |
| **5. 集成Dramatiq** | 添加 Dramatiq worker 服务，配置Redis为broker，并创建一个简单的异步测试任务。 | 后端API可以成功发布任务，worker可以成功消费任务。 |

### 阶段二：核心服务开发 (Week 3-4)

| 任务 | 描述 | 产出物 |
| :--- | :--- | :--- |
| **1. 用户服务 (User Service)** | 实现基于模板的用户认证、注册、登录功能。 | 可用的用户管理API。 |
| **2. 文档服务 (Document Service)** | 实现文档上传、解析、切块的API，并通过Dramatiq任务进行处理。 | 用户可以上传文档，文档被异步处理并存储。 |
| **3. 向量化服务 (Embedding Service)** | 实现文本向量化接口，支持可插拔的第三方服务。 | 文档块可以被成功向量化。 |
| **4. 上下文引擎 (Manticore)** | 将处理后的文本块和向量写入Manticore Search。 | Manticore中包含可供搜索的知识库索引。 |

### 阶段三：高级功能实现 (Week 5-6)

| 任务 | 描述 | 产出物 |
| :--- | :--- | :--- |
| **1. 对话服务 (Conversation Service)** | 实现对话管理的核心逻辑，集成上下文检索和LLM调用。 | 用户可以与AI进行基本的多轮对话。 |
| **2. 主题服务 (Topic Service)** | 实现学习主题的创建和管理，将对话与文档关联到主题。 | 对话和文档可以被有效组织。 |
| **3. 摘要服务 (Summary Service)** | 实现对话摘要的异步生成和存储。 | 每次对话后能自动生成学习笔记。 |
| **4. 前端界面 (Web UI)** | 基于已选定的React框架，开发核心的对话、文档上传和摘要展示界面。 | 一个可用的最小化可行产品 (MVP)。 |

### 阶段四：生产准备与部署 (Week 7-8)

| 任务 | 描述 | 产出物 |
| :--- | :--- | :--- |
| **1. CI/CD流水线** | 配置GitHub Actions，实现自动化测试、构建和部署。 | 完整的CI/CD流水线。 |
| **2. 可观测性** | 集成Sentry进行错误追踪和性能监控。 | Sentry后台可以看到应用的性能数据和错误报告。 |
| **3. 安全加固** | 使用云厂商的密钥管理服务管理所有敏感信息，遵循已制定的配置管理策略。 | 生产环境配置安全可靠。 |
| **4. 生产部署** | 在托管式容器平台（如Cloud Run）上完成首次生产部署。 | 项目成功上线。 |
