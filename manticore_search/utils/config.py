"""
配置管理模块

统一管理 Manticore Search 模块的所有配置项
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """应用配置类"""
    
    # Manticore 连接配置
    manticore_host: str = Field(default="localhost", description="Manticore 主机地址")
    manticore_port: int = Field(default=9306, description="Manticore MySQL 协议端口")
    manticore_http_port: int = Field(default=9308, description="Manticore HTTP API 端口")
    manticore_user: str = Field(default="", description="Manticore 用户名")
    manticore_password: str = Field(default="", description="Manticore 密码")
    manticore_charset: str = Field(default="utf8mb4", description="字符集")
    
    # 连接池配置
    connection_pool_size: int = Field(default=10, description="连接池大小")
    connection_timeout: int = Field(default=30, description="连接超时时间(秒)")
    read_timeout: int = Field(default=30, description="读取超时时间(秒)")
    
    # 搜索配置
    default_search_limit: int = Field(default=20, description="默认搜索结果数量")
    max_search_limit: int = Field(default=100, description="最大搜索结果数量")
    snippet_length: int = Field(default=200, description="摘要长度")
    snippet_around: int = Field(default=5, description="摘要前后词数")
    
    # 向量搜索配置
    vector_dimensions: int = Field(default=1536, description="向量维度 (OpenAI text-embedding-ada-002 默认1536)")
    knn_search_limit: int = Field(default=10, description="KNN 搜索结果数量")

    # HNSW 算法配置
    hnsw_m: int = Field(default=16, description="HNSW 邻居数量参数", ge=4, le=64)
    hnsw_ef_construction: int = Field(default=200, description="HNSW 索引构建时的搜索参数", ge=100, le=1000)
    hnsw_similarity: str = Field(default="cosine", description="向量相似度度量方式", pattern="^(l2|ip|cosine)$")

    # 向量量化配置
    vector_quantization: str = Field(default="none", description="向量量化方式", pattern="^(none|1bit|4bit|8bit)$")

    # 动态 ef 参数配置
    ef_search_min: int = Field(default=50, description="最小 ef 搜索参数", ge=10, le=200)
    ef_search_max: int = Field(default=400, description="最大 ef 搜索参数", ge=100, le=1000)
    ef_search_auto: bool = Field(default=True, description="是否启用动态 ef 调优")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")

    # 环境配置
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=False, description="是否启用调试模式")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # API 配置
    api_host: str = Field(default="0.0.0.0", description="API服务监听地址")
    api_port: int = Field(default=9000, ge=1024, le=65535, description="API服务端口")
    api_title: str = Field(default="Manticore Search API", description="API 标题")
    api_description: str = Field(
        default="基于 Manticore Search 的高内聚搜索引擎接口",
        description="API 描述"
    )
    api_version: str = Field(default="1.0.0", description="API 版本")
    api_prefix: str = Field(default="/api/v1", description="API 路径前缀")
    
    # 健康检查配置
    health_check_timeout: int = Field(default=5, description="健康检查超时时间(秒)")
    
    # 表配置
    default_table_name: str = Field(default="knowledge_base", description="默认表名")
    
    class Config:
        env_prefix = "MANTICORE_"
        case_sensitive = False
        
    def get_mysql_dsn(self) -> str:
        """获取 MySQL 协议连接字符串"""
        return (
            f"mysql://{self.manticore_user}:{self.manticore_password}@"
            f"{self.manticore_host}:{self.manticore_port}/"
        )
    
    def get_http_url(self) -> str:
        """获取 HTTP API 基础 URL"""
        return f"http://{self.manticore_host}:{self.manticore_http_port}"


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()


# 环境变量配置示例
def create_env_template() -> str:
    """创建环境变量配置模板"""
    return """
# Manticore Search 配置
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_USER=
MANTICORE_PASSWORD=
MANTICORE_CHARSET=utf8mb4

# 连接池配置
MANTICORE_CONNECTION_POOL_SIZE=10
MANTICORE_CONNECTION_TIMEOUT=30
MANTICORE_READ_TIMEOUT=30

# 搜索配置
MANTICORE_DEFAULT_SEARCH_LIMIT=20
MANTICORE_MAX_SEARCH_LIMIT=100
MANTICORE_SNIPPET_LENGTH=200
MANTICORE_SNIPPET_AROUND=5

# 向量搜索配置
MANTICORE_VECTOR_DIMENSIONS=1536
MANTICORE_KNN_SEARCH_LIMIT=10

# 日志配置
MANTICORE_LOG_LEVEL=INFO

# API 配置
MANTICORE_API_TITLE="Manticore Search API"
MANTICORE_API_DESCRIPTION="基于 Manticore Search 的高内聚搜索引擎接口"
MANTICORE_API_VERSION="1.0.0"
MANTICORE_API_PREFIX="/api/v1"

# 健康检查配置
MANTICORE_HEALTH_CHECK_TIMEOUT=5

# 表配置
MANTICORE_DEFAULT_TABLE_NAME=knowledge_base
""".strip()


if __name__ == "__main__":
    # 测试配置
    settings = get_settings()
    print("配置加载成功:")
    print(f"Manticore Host: {settings.manticore_host}")
    print(f"Manticore Port: {settings.manticore_port}")
    print(f"MySQL DSN: {settings.get_mysql_dsn()}")
    print(f"HTTP URL: {settings.get_http_url()}")
