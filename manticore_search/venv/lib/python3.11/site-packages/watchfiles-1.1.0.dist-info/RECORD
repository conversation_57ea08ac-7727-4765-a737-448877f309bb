../../../bin/watchfiles,sha256=C3X9QBJaZMWCljxn8gbLgPqy9oOm7wYJb-xtFAlmg6s,245
watchfiles-1.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
watchfiles-1.1.0.dist-info/METADATA,sha256=x7oD6uZyYpfAQNcBLAu_DXEAwXgSk-j5fMjHvdn-lwI,4874
watchfiles-1.1.0.dist-info/RECORD,,
watchfiles-1.1.0.dist-info/WHEEL,sha256=8hL2oHqulIPF_TJ_OfB-8kD2X9O6HJL0emTmeguFbqc,104
watchfiles-1.1.0.dist-info/entry_points.txt,sha256=s1Dpa2d_KKBy-jKREWW60Z3GoRZ3JpCEo_9iYDt6hOQ,48
watchfiles-1.1.0.dist-info/licenses/LICENSE,sha256=Nrb5inpC3jnhTxxutZgxzblMwRsF7q0xyB-4-FHRdQs,1110
watchfiles/__init__.py,sha256=IRlM9KOSedMzF1fvLr7yEHPVS-UFERNThlB-tmWI8yU,364
watchfiles/__main__.py,sha256=JgErYkiskih8Y6oRwowALtR-rwQhAAdqOYWjQraRIPI,59
watchfiles/__pycache__/__init__.cpython-311.pyc,,
watchfiles/__pycache__/__main__.cpython-311.pyc,,
watchfiles/__pycache__/cli.cpython-311.pyc,,
watchfiles/__pycache__/filters.cpython-311.pyc,,
watchfiles/__pycache__/main.cpython-311.pyc,,
watchfiles/__pycache__/run.cpython-311.pyc,,
watchfiles/__pycache__/version.cpython-311.pyc,,
watchfiles/_rust_notify.cpython-311-darwin.so,sha256=P8QaGzyyE9H-wOkn8EMUG0oeISZTx7MiknpaRNW4s5g,931056
watchfiles/_rust_notify.pyi,sha256=q5FQkXgBJEFPt9RCf7my4wP5RM1FwSVpqf221csyebg,4753
watchfiles/cli.py,sha256=DHMI0LfT7hOrWai_Y4RP_vvTvVdtcDaioixXLiv2pG4,7707
watchfiles/filters.py,sha256=U0zXGOeg9dMHkT51-56BKpRrWIu95lPq0HDR_ZB4oDE,5139
watchfiles/main.py,sha256=-pbJBFBA34VEXMt8VGcaPTQHAjsGhPf7Psu1gP_HnKk,15235
watchfiles/py.typed,sha256=MS4Na3to9VTGPy_8wBQM_6mNKaX4qIpi5-w7_LZB-8I,69
watchfiles/run.py,sha256=TLXb2y_xYx-t3xyszVQWHoGyG7RCb107Q0NoIcSWmjQ,15348
watchfiles/version.py,sha256=NRWUnkZ32DamsNKV20EetagIGTLDMMUnqDWVGFFA2WQ,85
