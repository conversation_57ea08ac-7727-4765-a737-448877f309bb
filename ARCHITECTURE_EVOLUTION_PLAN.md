# 架构演进计划：基于 Full-Stack FastAPI Template

**版本**: 1.0
**日期**: 2025-08-13

## 1. 核心战略决策

经过深入分析和讨论，我们决定采用 [fastapi/full-stack-fastapi-template](https://github.com/fastapi/full-stack-fastapi-template) 作为我们新项目的技术基础。

**核心思想**：最大化地利用该模板提供的最佳实践和开箱即用的功能（如用户认证、CI/CD、前后端分离架构等），并在此基础上，投入资源集成我们自己的核心特色服务。

本计划将取代旧的 `final-architecture-review.md`，成为指导后续开发的核心文档。

## 2. 高级别行动计划 (High-Level Action Plan)

我们将按照以下步骤，分阶段地完成新架构的构建：

### **阶段一：项目初始化与环境搭建**

1.  **[TODO]** 使用 `copier` 工具，基于模板生成一个新的、干净的项目仓库。
2.  **[TODO]** 初始化项目配置，包括 `.env` 文件、项目名称、域名等。
3.  **[TODO]** 在本地成功运行起原版模板的所有服务 (`docker-compose up`)，确保基础环境通畅。

### **阶段二：核心特色服务集成**

1.  **[TODO] 集成 Manticore Search**
    - 在 `docker-compose.yml` 中添加 `manticore` 服务。
    - 编写或迁移 Manticore 的异步客户端 (`manticore_client.py`)。
    - 在 FastAPI 后端中提供与 Manticore 交互的 API。

2.  **[TODO] 集成 Dramatiq (后台任务队列)**
    - 在 `docker-compose.yml` 中添加 `dramatiq_worker` 服务。
    - 配置 Redis 作为 Dramatiq 的 Broker。
    - 在 FastAPI 应用中定义 `async` 的 `dramatiq` actors。

### **阶段三：技术栈对齐与评估**

1.  **[TODO] ORM 方案评估**
    - **目标**: 统一项目的 ORM 工具。
    - **任务**: 评估现有服务代码从 `SQLAlchemy` 迁移到 `SQLModel` 的工作量和必要性。产出一份简短的迁移报告，做出最终决策。

2.  **[TODO] 前端方案决策**
    - **目标**: 确定最终的前端技术选型。
    - **任务**: 评估团队对模板自带的 `React` 技术栈的接受度。如果接受，则直接使用；如果不接受，则制定剥离或替换前端的计划。

### **阶段四：文档与规范更新**

1.  **[TODO]** 编写一份全新的、详细的架构文档，描述这个集成了我们所有功能的新系统架构。
2.  **[TODO]** 制定并更新团队的开发规范，以匹配新模板的代码风格和项目结构。

---

这个计划为我们指明了清晰的前进方向。接下来，我们将逐一完成这些任务。
