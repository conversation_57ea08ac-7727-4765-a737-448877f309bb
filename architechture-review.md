审查报告：技术栈与架构评估

概要

目标：生成基于代码库文档的审查总结，指出优缺点、风险与建议，供后续详细对比使用

已阅读的关键文档与位置

- [`ARCHITECTURE.md`](ARCHITECTURE.md:1)
- [`manticore_search/docs/architecture/tech-stack.md`](manticore_search/docs/architecture/tech-stack.md:1)
- [`README.md`](README.md:1)
- [`docker-compose.yml`](docker-compose.yml:1)
- [`requirements.txt`](requirements.txt:1)

高层结论（摘要）

- 当前技术栈适合 MVP：Python 3.11 + FastAPI + Manticore + PostgreSQL + Redis + Docker Compose
- 需要尽快明确异步/同步边界，修正可能阻塞的同步驱动调用
- 为生产环境需补充部署、可观测性与备份/HA 的实施方案

逐项审查（要点、风险与短建议）

1) 后端与框架：Python 3.11 + FastAPI
- 要点：开发效率高，类型安全，原生 async 支持（见 [`manticore_search/docs/architecture/tech-stack.md`](manticore_search/docs/architecture/tech-stack.md:22)）
- 风险：CPU 密集（模型推理）或 IO 阻塞需分离处理
- 建议：保留 FastAPI，使用专用 worker 或队列处理模型推理

2) 搜索与向量：Manticore Search
- 要点：支持全文+向量，SQL 接口，资源占用较低（见 [`manticore_search/docs/architecture/tech-stack.md`](manticore_search/docs/architecture/tech-stack.md:108)）
- 风险：生态与托管选项有限；大规模向量库时需评估扩展性
- 建议：当前阶段保留；未来评估 Milvus/Weaviate/Elastic Vector 的迁移成本

3) 数据库：PostgreSQL + Redis
- 要点：成熟、稳定，已在 `docker-compose.yml` 中配置
- 风险：需明确备份/HA/监控策略
- 建议：使用连接池，优先 async 驱动（asyncpg + SQLAlchemy async）

4) 驱动与异步策略一致性
- 问题：requirements 同时包含同步与异步客户端（pymysql, psycopg2-binary 与 aioredis）
- 风险：同步驱动在 async 事件循环中会阻塞，影响吞吐
- 建议：明确异步优先策略；对必须同步的依赖放到线程池或独立同步 worker

5) 向量化 / Embedding：本地 sentence-transformers + 可选 faiss
- 要点：本地模型低延迟但资源占用高，模型管理复杂
- 风险：GPU/内存管理、版本管理和运维成本
- 建议：小规模阶段本地模型；生产评估托管推理或分离推理服务（BentoML/TorchServe/云API）

6) 异步任务队列：Celery（已在依赖）
- 要点：成熟方案，支持重试与任务编排
- 风险：与 asyncio 生态整合成本，需 broker（Redis/RabbitMQ）与监控
- 建议：评估 dramatiq 或结合 Redis Streams 的 asyncio 方案；若保留 Celery，明确 broker 与监控

7) 容器化与部署：Docker Compose（当前）
- 要点：本地开发与集成便捷，docker-compose.yml 已覆盖完整栈
- 风险：不利于生产弹性扩缩、滚动升级与服务发现
- 建议：制定从 Compose 到 Kubernetes/云托管的迁移路线与优先级

观测、日志与安全（要点）
- 已预留 Prometheus/Grafana、OpenTelemetry 接口（ARCHITECTURE 中提及）
- 建议尽快实现基础指标与 Alert；生产使用 Secret Manager 替代 .env

优先级建议（短中长期）
- 立即（1-2 周）：修正异步/同步驱动边界、为 Manticore 调用设计线程池或专用服务；补充基础监控（健康、延迟、错误率）
- 中期（1-3 月）：部署策略落地（K8s 或云托管），模型推理服务化，备份/HA 策略实现
- 长期（3-12 月）：评估向量库替换（若需要）、弹性扩缩与多区域部署、完善 SLO/SLA 与自动化运维

风险与迁移成本（概览）
- 将 pymysql 替换为异步方案或隔离到 worker：低至中等工程量（几天到 2 周）
- 将 embedding 推理迁移到托管/独立推理服务：中等至高（取决于是否使用 GPU 与模型数量，数周到数月）
- 从 Compose 迁移到 Kubernetes：中等至高（取决 CI/CD 与运维经验，数周至数月）

下一步与交付物
- 我将把本报告保存为文件并在仓库中提交为审查草案；随后按你的要求深入对比替代方案并生成决策矩阵
- 请确认是否将本报告写入 [`architechture-review.md`](architechture-review.md:1) 或其他文件名