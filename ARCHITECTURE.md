# 知深学习导师 - 系统架构文档

## 📋 项目概述

**项目名称**: 知深学习导师 (Phoenix 项目)  
**核心目标**: 打造个人化AI学习伴行系统，通过可追溯、可复盘的引导式对话，帮助用户真正内化知识  
**技术特色**: 基于Manticore Search的智能上下文引擎，突破传统AI对话的上下文窗口限制

## 🏗️ 整体架构

### 分层架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 Frontend Layer                    │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │   Web UI    │ │  WebSocket  │                           │
│  │ (React/Vue) │ │   Client    │                           │
│  └─────────────┘ └─────────────┘                           │
├─────────────────────────────────────────────────────────────┤
│                  API网关层 API Gateway Layer                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │        API Gateway (路由/认证/限流)                      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  应用服务层 Application Services             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Conversation│ │   Topic     │ │  Summary    │          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐                          │
│  │  Document   │ │    User     │                          │
│  │  Service    │ │  Service    │                          │
│  └─────────────┘ └─────────────┘                          │
├─────────────────────────────────────────────────────────────┤
│                  核心引擎层 Core Engine Layer                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Manticore   │ │     AI      │ │ Embedding   │          │
│  │ Search ✅   │ │Integration  │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  数据持久层 Data Persistence Layer          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Manticore   │ │ PostgreSQL  │ │   Redis     │          │
│  │     DB      │ │    (关系)    │ │  (缓存)     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 模块目录结构

```
master-know/
├── manticore_search/           # ✅ 智能上下文引擎 (已完成)
├── embedding_service/          # 🔥 向量化引擎 (Priority 1)
├── user_service/              # 👤 用户服务 (Priority 2)
├── api_gateway/               # 🚪 API网关 (Priority 3)
├── topic_service/             # 📚 主题服务 (Priority 4)
├── document_service/          # 📄 文档服务 (Priority 5)
├── llm_integration/           # 🤖 AI对话引擎 (Priority 6)
├── conversation_service/      # 💬 对话服务 (Priority 7)
├── summary_service/           # 📝 摘要服务 (Priority 8)
├── web_ui/                    # 🖥️ 前端界面 (Priority 9)
├── docs/                      # 📖 项目文档
├── scripts/                   # 🔧 部署和工具脚本
├── docker-compose.yml         # 🐳 服务编排
├── requirements.txt           # 📦 Python依赖
├── ARCHITECTURE.md            # 🏗️ 架构文档 (本文件)
└── README.md                  # 📋 项目说明
```

## 🎯 实现优先级与阶段规划 (调整版)

### Phase 1: 核心基础 (Week 1)
**目标**: 快速搭建核心功能基础，让系统能跑起来

1. **embedding_service** (Priority 1)
   - 文本向量化能力
   - 与manticore_search集成
   - 支持批量处理

2. **user_service** (Priority 2 - 简化版)
   - 创建固定测试用户 (user_id=1, username="test_user")
   - 简单的用户信息返回接口
   - 无需复杂认证，直接返回测试用户

3. **api_gateway** (Priority 3 - 简化版)
   - 简单的请求转发代理
   - 路由映射: /api/v1/topics -> topic_service:8004
   - 路由映射: /api/v1/documents -> document_service:8005
   - 无需认证中间件，直接透传

### Phase 2: 核心功能 (Week 2)
**目标**: 实现MVP的核心业务功能，用户能创建主题和上传文档

4. **topic_service** (Priority 4 - 立即开发)
   - 主题创建和管理 (固定user_id=1)
   - 文档关联功能
   - 主题列表和详情

5. **document_service** (Priority 5 - 立即开发)
   - 文档上传和解析 (.txt, .md)
   - 智能文档分块
   - 与embedding_service和manticore_search集成

6. **llm_integration** (Priority 6)
   - LLM模型接口
   - 提示工程
   - 上下文管理

### Phase 3: 高级功能 (Week 5-6)
**目标**: 完成用户体验和高级功能

7. **conversation_service** (Priority 7)
   - 对话管理
   - 实时通信
   - 历史记录

8. **summary_service** (Priority 8)
   - 自动摘要生成
   - 回溯映射
   - 摘要优化

9. **web_ui** (Priority 9)
   - 双栏界面
   - 实时交互
   - 摘要回溯

## 🔗 模块间依赖关系

### 核心依赖链
```
manticore_search → embedding_service → document_service
                                    ↓
user_service → api_gateway → topic_service → llm_integration
                                           ↓
                            conversation_service → summary_service → web_ui
```

### 关键接口
- **embedding_service** ↔ **manticore_search**: 向量存储和检索
- **llm_integration** ↔ **manticore_search**: 上下文检索
- **conversation_service** ↔ **summary_service**: 摘要生成触发
- **web_ui** ↔ **conversation_service**: WebSocket实时通信

## 🚀 快速开始指南

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd master-know

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# 安装基础依赖
pip install -r requirements.txt
```

### 2. 启动基础服务
```bash
# 启动Manticore Search (已完成模块)
cd manticore_search
docker-compose up -d
cd ..
```

### 3. 按优先级开发模块
```bash
# 开始第一个模块 - 向量化引擎
cd embedding_service
# 按照该模块README.md进行开发
```

## 📊 技术栈总览

### 后端技术栈
- **Python 3.11**: 主要开发语言
- **FastAPI**: Web框架和API服务
- **Pydantic**: 数据验证和序列化
- **Manticore Search**: 搜索引擎和向量数据库
- **PostgreSQL**: 关系型数据存储
- **Redis**: 缓存和会话存储

### 前端技术栈
- **React/Vue.js**: 前端框架
- **WebSocket**: 实时通信
- **Tailwind CSS**: 样式框架

### 基础设施
- **Docker**: 容器化部署
- **Docker Compose**: 服务编排
- **Nginx**: 反向代理和负载均衡

## 🎯 MVP功能范围

### 核心功能 (必须实现)
- [x] 智能上下文搜索 (manticore_search)
- [ ] 主题式对话管理
- [ ] 文档上传和处理
- [ ] AI引导式学习对话
- [ ] 实时摘要生成
- [ ] 摘要回溯功能
- [ ] 双栏学习界面

### 扩展功能 (后续版本)
- [ ] 多用户支持
- [ ] 高级文档格式支持
- [ ] 跨主题智能关联
- [ ] 学习进度分析
- [ ] 移动端支持

## 🏗️ 架构演进原则

### 核心理念：先立骨架，再长肌肉

我们采用"演进式架构"策略，平衡快速迭代与完美架构之间的矛盾。对每一项技术决策，都问自己：**"这件事如果我现在不做，以后再做的成本有多高？"**

- **高成本的，现在必须做（地基）**
- **中等成本的，现在预留接口（预埋管道）**
- **低成本的，果断放到以后做（精装修）**

### 三阶段演进策略

#### 🔴 第一阶段：地基建设（必须立即做）
**原则**: 这些是架构的根基，如果现在不做，以后返工成本是10倍甚至100倍

1. **统一配置管理**
   - 所有服务采用12-factor配置规范
   - 统一.env模板与加载顺序
   - 敏感信息密钥管理（避免硬编码）

2. **标准健康检查**
   - 所有服务提供/health和/ready端点
   - 统一健康检查响应格式
   - 服务依赖关系的健康传播

3. **异步处理架构**
   - 文档处理→向量化→入库的异步流水线
   - 事件驱动模式与消息队列
   - 幂等性设计与错误重试机制

4. **服务边界明确**
   - API版本化与契约定义
   - 服务间通信协议标准化
   - 数据一致性模型设计

#### 🟡 第二阶段：管道预埋（预留接口）
**原则**: 我们知道未来肯定需要，但现在不必完全实现，先占个位置

1. **可观测性框架**
   - 结构化日志格式（预留trace_id字段）
   - 指标采集接口（预留Prometheus端点）
   - 链路追踪预留（OpenTelemetry兼容）

2. **权限模型框架**
   - 基础RBAC权限接口设计
   - 多租户数据隔离预留
   - 审计日志接口预留

3. **RAG策略抽象**
   - 检索策略接口封装
   - 模型切换抽象层
   - 提示工程版本化接口

4. **性能监控预留**
   - SLO/SLI指标定义接口
   - 容量规划数据采集点
   - 降级策略触发接口

#### 🟢 第三阶段：精装修（后续迭代）
**原则**: 这些功能很重要，但可以作为"插件"在系统稳定运行后逐步添加

1. **完整CI/CD流水线**
2. **专业监控告警系统**
3. **高级安全方案**
4. **多租户与合规功能**

## 📝 开发规范

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用类型注解 (Type Hints)
- 编写单元测试和集成测试
- 使用docstring文档化函数和类

### 模块设计原则
- **高内聚低耦合**: 模块内功能紧密相关，模块间依赖最小
- **接口优先**: 先定义清晰的接口，再实现具体功能
- **可测试性**: 每个模块都要有完整的测试覆盖
- **可演进性**: 设计要考虑未来功能扩展和架构演进

---

**文档版本**: 1.1.0
**创建日期**: 2025-08-13
**最后更新**: 2025-08-13
**维护者**: Winston (Architect)
