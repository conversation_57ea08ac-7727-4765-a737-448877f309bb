# 主题服务 (Topic Service)

**优先级**: Priority 4 - 核心功能模块  
**状态**: 📚 待开发  
**依赖**: user_service, api_gateway

## 🎯 模块职责

主题服务是知深学习导师的核心业务模块，负责管理用户的学习主题生命周期，包括主题创建、文档关联、主题管理等功能。

### 核心功能
- **主题创建**: 创建新的学习主题
- **主题管理**: 主题的增删改查操作
- **文档关联**: 管理主题与文档的多对多关系
- **主题列表**: 用户主题列表展示和搜索
- **主题统计**: 学习进度和统计信息

## 🏗️ 技术架构

### 技术栈
- **Python 3.11**: 主要开发语言
- **FastAPI**: API服务框架
- **PostgreSQL**: 关系数据存储
- **SQLAlchemy**: ORM框架
- **Pydantic**: 数据验证

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Topic     │ │  Document   │ │ Statistics  │          │
│  │   Routes    │ │   Link      │ │   Routes    │          │
│  │             │ │  Routes     │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Topic     │ │  Document   │ │ Statistics  │          │
│  │  Service    │ │    Link     │ │  Service    │          │
│  │             │ │  Service    │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                PostgreSQL Database                     │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │   topics    │ │ topic_doc   │ │ topic_stats │      │ │
│  │  │    table    │ │   links     │ │   table     │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
topic_service/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── topic_service.py    # 主题管理服务
│   ├── document_link_service.py # 文档关联服务
│   └── statistics_service.py   # 统计服务
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── topic.py           # 主题模型
│   ├── document_link.py   # 文档关联模型
│   └── statistics.py      # 统计模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库连接
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_topic_service.py
│   ├── test_document_link_service.py
│   └── test_statistics_service.py
├── docs/                   # 文档目录
│   └── api-documentation.md
├── migrations/             # 数据库迁移
│   └── init_topics.sql
├── requirements.txt        # 依赖包列表
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.11
- PostgreSQL 13+
- user_service (用户认证)

### 1. 安装依赖
```bash
cd topic_service
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 初始化数据库
```bash
# 运行数据库迁移
python -c "from utils.database import init_db; init_db()"
```

### 3. 配置环境变量
```bash
# DATABASE_URL=postgresql://user:pass@localhost:5432/master_know
# USER_SERVICE_URL=http://localhost:8002
```

### 4. 运行测试
```bash
python test_module.py
```

### 5. 启动服务
```bash
uvicorn api.main:app --reload --port 8004
```

## 💻 使用示例

### 创建主题
```python
from topic_service import TopicService, TopicCreate

topic_service = TopicService()

# 创建新主题
topic_data = TopicCreate(
    title="Python深度学习",
    description="学习Python在深度学习中的应用",
    user_id=1
)

topic = topic_service.create_topic(topic_data)
print(f"主题创建成功: {topic.title}")
```

### 关联文档
```python
from topic_service import DocumentLinkService

link_service = DocumentLinkService()

# 关联文档到主题
link_service.link_document_to_topic(
    topic_id=topic.id,
    document_id=123,
    user_id=1
)
```

### 获取主题列表
```python
# 获取用户的所有主题
topics = topic_service.get_user_topics(
    user_id=1,
    limit=20,
    offset=0
)

for topic in topics:
    print(f"主题: {topic.title} - 文档数: {topic.document_count}")
```

## 🔗 API接口设计

### 主题管理接口
```python
# 创建主题
POST /api/v1/topics
{
    "title": "Python深度学习",
    "description": "学习Python在深度学习中的应用"
}

# 获取主题列表
GET /api/v1/topics?limit=20&offset=0

# 获取主题详情
GET /api/v1/topics/{topic_id}

# 更新主题
PUT /api/v1/topics/{topic_id}
{
    "title": "新标题",
    "description": "新描述"
}

# 删除主题
DELETE /api/v1/topics/{topic_id}
```

### 文档关联接口
```python
# 关联文档到主题
POST /api/v1/topics/{topic_id}/documents
{
    "document_id": 123
}

# 获取主题的文档列表
GET /api/v1/topics/{topic_id}/documents

# 取消文档关联
DELETE /api/v1/topics/{topic_id}/documents/{document_id}
```

### 统计接口
```python
# 获取主题统计
GET /api/v1/topics/{topic_id}/stats

# 获取用户学习统计
GET /api/v1/users/me/topic-stats
```

## 🗄️ 数据模型设计

### 主题表 (topics)
```sql
CREATE TABLE topics (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE INDEX idx_topics_user_id ON topics(user_id);
CREATE INDEX idx_topics_title ON topics USING gin(to_tsvector('english', title));
```

### 主题文档关联表 (topic_document_links)
```sql
CREATE TABLE topic_document_links (
    id BIGSERIAL PRIMARY KEY,
    topic_id BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(topic_id, document_id)
);

CREATE INDEX idx_topic_doc_links_topic ON topic_document_links(topic_id);
CREATE INDEX idx_topic_doc_links_doc ON topic_document_links(document_id);
```

### 主题统计表 (topic_statistics)
```sql
CREATE TABLE topic_statistics (
    id BIGSERIAL PRIMARY KEY,
    topic_id BIGINT NOT NULL UNIQUE,
    document_count INTEGER DEFAULT 0,
    conversation_count INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP,
    total_study_time INTEGER DEFAULT 0, -- 秒
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔗 与其他模块集成

### 与用户服务集成
```python
# 验证用户权限
from user_service import get_current_user

async def create_topic(topic_data: TopicCreate, current_user = Depends(get_current_user)):
    topic_data.user_id = current_user.id
    return topic_service.create_topic(topic_data)
```

### 与文档服务集成
```python
# 验证文档存在性
from document_service import DocumentService

def link_document_to_topic(topic_id: int, document_id: int):
    # 验证文档存在
    document = document_service.get_document(document_id)
    if not document:
        raise DocumentNotFoundError()
    
    return link_service.create_link(topic_id, document_id)
```

## 🧪 测试策略

### 单元测试
- 主题CRUD操作测试
- 文档关联功能测试
- 统计计算测试
- 权限验证测试

### 集成测试
- 数据库集成测试
- 用户服务集成测试
- API接口测试

### 业务逻辑测试
- 主题创建流程测试
- 文档关联业务测试
- 统计更新测试

## 📊 性能指标

### 目标性能
- **主题创建**: <100ms
- **主题列表**: <200ms
- **文档关联**: <50ms
- **统计查询**: <100ms

### 优化策略
- 数据库索引优化
- 查询语句优化
- 缓存热点数据
- 分页查询优化

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/master_know

# 服务依赖
USER_SERVICE_URL=http://localhost:8002
DOCUMENT_SERVICE_URL=http://localhost:8005

# API配置
API_HOST=0.0.0.0
API_PORT=8004

# 业务配置
MAX_TOPICS_PER_USER=100
MAX_DOCUMENTS_PER_TOPIC=50
```

## 🚧 开发计划

### Phase 1: 基础功能 (Week 3)
- [x] 项目结构搭建
- [ ] 主题CRUD功能
- [ ] 数据库模型设计
- [ ] 基础API接口

### Phase 2: 高级功能 (Week 4)
- [ ] 文档关联功能
- [ ] 统计功能
- [ ] 搜索功能
- [ ] 权限集成

---

**模块版本**: 1.0.0  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
