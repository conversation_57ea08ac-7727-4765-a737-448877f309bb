### **3. 初步设计草稿 (Preliminary Design Draft - v1.0)**

#### **3.1. 核心数据模型 (Core Data Model)**

##### **1.** `topics` 表

管理用户创建的每一个学习主题。

|字段名 |类型 |描述 |
|:---|:---|:---|
|`topic_id` |`bigint` |主键，唯一标识 |
|`user_id` |`bigint` |关联的用户ID |
|`title` |`text` |主题标题 (indexed) |
|`created_at` |`timestamp` |创建时间 |

##### **2.** `documents` 表

存储所有知识源的切片信息，与主题解耦。

|字段名 |类型 |描述 |
|:---|:---|:---|
|`doc_id` |`bigint` |主键，唯一标识一个完整的知识源 |
|`chunk_id` |`bigint` |唯一标识一个文档切片 |
|`chunk_text` |`text` |切片原文 (indexed) |
|`chunk_vector` |`vector` |切片原文的向量 |

##### **3.** `topic_document_links` 表

记录主题与文档之间“多对多”的关联关系。

|字段名 |类型 |描述 |
|:---|:---|:---|
|`topic_id` |`bigint` |关联的主题ID |
|`doc_id` |`bigint` |关联的文档ID |

###### **4. `conversations` 表**
记录所有对话消息，是系统的核心事实表。

| 字段名         | 类型     | 描述                                                                                                           |
| :--------------- | :------- | :------------------------------------------------------------------------------------------------------------- |
| `message_id`     | `bigint` | **主键**。唯一标识一条物理消息。                                                                                 |
| `turn_id`        | `bigint` | **逻辑回合ID**。用于将多条物理消息组合成一个有意义的交互单元 (indexed)。                                         |
| `topic_id`       | `bigint` | 标识消息所属的主题 (indexed)。                                                                                 |
| `role`           | `string` | 发言角色：'user' 或 'assistant'。                                                                          |
| `content`        | `text`   | 消息原文。                                                                                                     |
| `summary`        | `json`   | 对整个`turn_id`的**结构化摘要**。存储在该回合最后一条AI消息上，格式为 `{"user": "...", "assistant": "..."}`。 |
| `summary_vector` | `vector` | 对`summary`中两个文本的值**合并后**进行向量化，生成一个代表整个回合语义的向量，用于AI的长期记忆检索。 |
| `created_at`     | `timestamp`| 消息创建时间。                                                                                                 |


