# 知深学习导师 (Phoenix 项目)

**个人化AI学习伴行系统** - 通过可追溯、可复盘的引导式对话，帮助用户真正内化知识

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Manticore](https://img.shields.io/badge/Manticore-6.0+-orange.svg)](https://manticoresearch.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 项目愿景

打造一款个人化AI学习伴行系统，核心目标不是"给答案"，而是通过可追溯、可复盘的引导式对话，帮助用户"真正内化"知识，将碎片化的学习沉淀为结构化的个人知识库。

### 核心特色
- **🧠 智能上下文引擎**: 基于Manticore Search的混合搜索，突破传统AI对话的上下文窗口限制
- **📚 主题式长期记忆**: 支持跨会话的连续学习，AI能记住你们聊到哪里了
- **📝 可交互的动态摘要**: 自动生成学习笔记，点击摘要可回溯到原对话
- **🔄 引导式学习对话**: AI通过提问、类比和启发，引导用户主动思考

## 🏗️ 系统架构

### 模块化设计
```
知深学习导师 (Phoenix)
├── ✅ manticore_search/      # 智能上下文引擎 (已完成)
├── 🔥 embedding_service/     # 向量化引擎 (Priority 1)
├── 👤 user_service/          # 用户服务 (Priority 2)
├── 🚪 api_gateway/           # API网关 (Priority 3)
├── 📚 topic_service/         # 主题服务 (Priority 4)
├── 📄 document_service/      # 文档服务 (Priority 5)
├── 🤖 llm_integration/       # AI对话引擎 (Priority 6)
├── 💬 conversation_service/  # 对话服务 (Priority 7)
├── 📝 summary_service/       # 摘要服务 (Priority 8)
└── 🖥️ web_ui/               # 前端界面 (Priority 9)
```

### 技术栈
- **后端**: Python 3.11, FastAPI, PostgreSQL, Redis
- **搜索引擎**: Manticore Search (全文+向量混合搜索)
- **AI集成**: OpenAI API, Claude API, 本地LLM支持
- **前端**: React/Vue.js, WebSocket实时通信
- **部署**: Docker, Docker Compose

## 🚀 快速开始

### 环境要求
- Python 3.11
- Docker & Docker Compose
- 4GB+ 内存
- PostgreSQL 13+
- Redis 6+

### 1. 克隆项目
```bash
git clone <repository-url>
cd master-know
```

### 2. 环境准备
```bash
# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# 安装基础依赖
pip install -r requirements.txt
```

### 3. 启动基础服务
```bash
# 启动Manticore Search (已完成模块)
cd manticore_search
docker-compose up -d
cd ..

# 验证Manticore服务
python -c "from manticore_search import get_settings; print('✅ Manticore Search 正常')"
```

### 4. 按优先级开发模块
```bash
# 开始第一个模块 - 向量化引擎
cd embedding_service
# 按照该模块README.md进行开发和测试
```

## 📋 开发路线图

### Phase 1: 基础设施 (Week 1-2)
- [x] **manticore_search** - 智能上下文引擎 ✅
- [ ] **embedding_service** - 向量化引擎 🔥
- [ ] **user_service** - 用户认证和管理
- [ ] **api_gateway** - 统一API网关

### Phase 2: 核心功能 (Week 3-4)
- [ ] **topic_service** - 主题管理
- [ ] **document_service** - 文档处理
- [ ] **llm_integration** - AI对话引擎

### Phase 3: 高级功能 (Week 5-6)
- [ ] **conversation_service** - 对话管理
- [ ] **summary_service** - 摘要生成
- [ ] **web_ui** - 用户界面

## 💻 使用示例

### 典型学习场景
```python
# 1. 创建学习主题
topic = create_topic("Python深度学习")

# 2. 上传学习资料
documents = upload_documents([
    "深度学习入门.md",
    "PyTorch教程.pdf"
])

# 3. 开始学习对话
conversation = start_conversation(topic_id=topic.id)

# 4. AI引导式学习
user: "我想学习神经网络，但不知道从哪里开始"
ai: "很好的问题！让我先了解一下你的背景。你之前有接触过机器学习吗？"
user: "只是听说过，没有实际经验"
ai: "那我们从最基础的概念开始。你觉得神经网络和人脑有什么相似之处？"

# 5. 自动生成学习摘要
summary = get_conversation_summary(conversation.id)
# 用户：想学习神经网络但不知道从哪开始
# AI：从基础概念入手，通过类比人脑帮助理解
```

### API使用示例
```python
# 智能搜索 (基于manticore_search)
from manticore_search import SearchService, create_hybrid_search_request

search_request = create_hybrid_search_request(
    query="深度学习优化算法",
    query_vector=embedding_vector,
    limit=10
)
results = search_service.search(search_request)

# 向量化文本 (embedding_service)
from embedding_service import EmbeddingService

embedding = embedding_service.embed_text("神经网络反向传播算法")
```

## 🔧 配置说明

### 环境变量配置
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# DATABASE_URL=postgresql://user:pass@localhost:5432/master_know
# REDIS_URL=redis://localhost:6379
# OPENAI_API_KEY=your_openai_key_here
# MANTICORE_HOST=localhost
# MANTICORE_PORT=9306
```

### 服务端口分配
- **manticore_search**: 9000 (已完成)
- **embedding_service**: 9001
- **user_service**: 9002
- **api_gateway**: 9003
- **topic_service**: 9004
- **document_service**: 9005
- **llm_integration**: 9006
- **conversation_service**: 9007
- **summary_service**: 9008
- **web_ui**: 3000

## 🧪 测试策略

### 运行测试
```bash
# 测试已完成的模块
cd manticore_search
./run_tests.sh

# 测试新开发的模块
cd embedding_service
python test_module.py
pytest tests/
```

### 集成测试
```bash
# 运行完整的集成测试
python scripts/testing/run_integration_tests.py
```

## 📚 文档资源

- **[架构文档](ARCHITECTURE.md)** - 完整的系统架构设计
- **[开发指南](docs/development-guide.md)** - 开发规范和最佳实践
- **[API文档](docs/api-documentation.md)** - 完整的API接口说明
- **[部署指南](docs/deployment-guide.md)** - 生产环境部署指南

### 模块文档
每个模块都有详细的README文档：
- [manticore_search/README.md](manticore_search/README.md) ✅
- [embedding_service/README.md](embedding_service/README.md)
- [user_service/README.md](user_service/README.md)
- [topic_service/README.md](topic_service/README.md)
- [conversation_service/README.md](conversation_service/README.md)

## 🤝 贡献指南

### 开发流程
1. **选择模块**: 按照优先级选择要开发的模块
2. **阅读文档**: 仔细阅读模块的README和架构设计
3. **搭建环境**: 按照模块README搭建开发环境
4. **编写代码**: 遵循代码规范和设计原则
5. **编写测试**: 确保测试覆盖率 > 80%
6. **集成测试**: 验证与其他模块的集成

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用类型注解 (Type Hints)
- 编写docstring文档
- 单元测试覆盖率 > 80%

## 📊 项目状态

### 完成情况
- ✅ **manticore_search**: 智能上下文引擎 (100%)
- 🔥 **embedding_service**: 向量化引擎 (0% - 开发中)
- ⏳ **其他模块**: 待开发

### 性能目标
- **搜索响应**: < 100ms
- **对话延迟**: < 200ms
- **并发用户**: 1000+
- **系统可用性**: 99.9%

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: Winston (Architect)
- **邮箱**: <EMAIL>
- **项目地址**: [GitHub Repository](https://github.com/your-org/master-know)

---

**让学习变得更深入，让知识真正内化** 🚀
