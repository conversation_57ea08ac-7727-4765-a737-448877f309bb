import asyncio
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Union, Optional, Dict, Any
import httpx
from openai import AsyncOpenAI
from models.embedding import (
    EmbeddingRequest,
    EmbeddingResponse,
    EmbeddingObject,
    Usage,
    LegacyEmbeddingRequest,
    LegacyEmbeddingResponse
)
from utils.config import Settings

# 导入manticore_search模块 (可选)
try:
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'manticore_search'))
    from clients.manticore_client import ManticoreClient
    from utils.config import get_settings as get_manticore_settings
    MANTICORE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Manticore Search not available: {e}")
    ManticoreClient = None
    get_manticore_settings = None
    MANTICORE_AVAILABLE = False

logger = logging.getLogger(__name__)

class EmbeddingService:
    def __init__(self, settings: Settings):
        self.settings = settings

        # 初始化 OpenAI 客户端
        self.client = AsyncOpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            timeout=settings.request_timeout,
            max_retries=settings.max_retries
        )

        # 初始化 Manticore 客户端
        try:
            manticore_settings = get_manticore_settings()
            self.manticore_client = ManticoreClient(manticore_settings)
            self.manticore_enabled = True
            logger.info("Manticore Search client initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Manticore client: {e}")
            self.manticore_client = None
            self.manticore_enabled = False

        logger.info(f"EmbeddingService initialized with base_url: {settings.openai_base_url}")

    async def create_embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """
        Create embeddings using OpenAI compatible API
        """
        try:
            # 确保 input 是列表格式
            if isinstance(request.input, str):
                input_texts = [request.input]
            else:
                input_texts = request.input

            logger.info(f"Creating embeddings for {len(input_texts)} texts using model {request.model}")

            # 调用 OpenAI API
            response = await self.client.embeddings.create(
                input=input_texts,
                model=request.model,
                encoding_format=request.encoding_format.value if request.encoding_format else "float",
                dimensions=request.dimensions,
                user=request.user
            )

            # 转换为我们的响应格式
            embedding_objects = []
            for i, embedding_data in enumerate(response.data):
                embedding_objects.append(EmbeddingObject(
                    embedding=embedding_data.embedding,
                    index=i
                ))

            usage = Usage(
                prompt_tokens=response.usage.prompt_tokens,
                total_tokens=response.usage.total_tokens
            )

            return EmbeddingResponse(
                data=embedding_objects,
                model=response.model,
                usage=usage
            )

        except Exception as e:
            logger.error(f"Error creating embeddings: {str(e)}")
            raise

    async def embed_single_text(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        便捷方法：为单个文本生成向量
        """
        request = EmbeddingRequest(
            input=text,
            model=model or self.settings.default_model
        )

        response = await self.create_embeddings(request)
        return response.data[0].embedding

    async def embed_batch_texts(self, texts: List[str], model: Optional[str] = None) -> List[List[float]]:
        """
        便捷方法：为多个文本生成向量
        """
        request = EmbeddingRequest(
            input=texts,
            model=model or self.settings.default_model
        )

        response = await self.create_embeddings(request)
        return [obj.embedding for obj in response.data]

    async def store_embeddings_in_manticore(
        self,
        texts: List[str],
        embeddings: List[List[float]],
        table_name: str = "embeddings",
        metadata: Optional[List[Dict[str, Any]]] = None,
        *,
        batch_size: int = 100,
        concurrency: int = 5,
        max_retries: int = 3,
        backoff_base: float = 0.5
    ) -> bool:
        """
        将向量批量存储到 Manticore Search 中，支持批量分片、并发 semaphore、重试与幂等 ID 生成策略。

        - batch_size: 每个子批次的大小
        - concurrency: 同时并发上传的协程数（由 semaphore 控制）
        - max_retries: 单个批次最大重试次数
        - backoff_base: 指数退避基数（秒）
        """
        if not self.manticore_enabled:
            logger.warning("Manticore Search not available, skipping storage")
            return False

        # 确保表存在
        if not await self._ensure_embedding_table_exists(table_name):
            return False

        # 稳定的 ID 生成函数：使用 document text hash + index to ensure id stability across retries/imports
        import hashlib
        import time
        import math
        import uuid

        def stable_doc_id(text: str, idx: int) -> int:
            # 生成基于文本和索引的确定性 64-bit 整数 id (取 md5 前8字节)
            h = hashlib.md5(f"{text}-{idx}".encode("utf-8")).digest()
            return int.from_bytes(h[:8], byteorder='big', signed=False)

        # 将所有文档组织为 dict 列表，但不一次性发送所有数据
        docs = []
        for i, (text, embedding) in enumerate(zip(texts, embeddings)):
            doc_id = stable_doc_id(text, i)
            doc = {
                'id': doc_id,
                'text': text,
                'embedding': embedding,  # 保持为原始浮点数组
                'dimension': len(embedding)
            }
            if metadata and i < len(metadata):
                doc.update(metadata[i])
            docs.append(doc)

        # 分割成子批次
        batches = [docs[i:i + batch_size] for i in range(0, len(docs), batch_size)]
        total = len(docs)
        success_total = 0

        semaphore = asyncio.Semaphore(concurrency)

        async def send_batch(batch: List[Dict[str, Any]]) -> int:
            """发送单个批次并在失败时进行重试，返回成功插入数量（估计）"""
            attempt = 0
            while attempt <= max_retries:
                try:
                    # 为阻塞/同步的 client 方法使用线程池执行（bulk_insert_documents 可能是同步）
                    loop = asyncio.get_running_loop()
                    # bulk_insert_documents 预期返回成功数量或抛出异常
                    success_count = await loop.run_in_executor(
                        None,
                        lambda: self.manticore_client.bulk_insert_documents(table_name, batch)
                    )
                    logger.info(f"Batch insert success: {success_count}/{len(batch)}")
                    return success_count if isinstance(success_count, int) else len(batch)
                except Exception as e:
                    attempt += 1
                    wait = backoff_base * (2 ** (attempt - 1))
                    # cap等待时间
                    wait = min(wait, 30.0)
                    logger.warning(f"Batch insert failed (attempt {attempt}/{max_retries}) - retrying in {wait}s: {e}")
                    await asyncio.sleep(wait)
            logger.error(f"Batch failed after {max_retries} attempts, marking {len(batch)} as failed")
            return 0

        # 并发执行所有批次
        async def worker(batch: List[Dict[str, Any]]):
            nonlocal success_total
            async with semaphore:
                cnt = await send_batch(batch)
                success_total += cnt

        tasks = [asyncio.create_task(worker(batch)) for batch in batches]
        await asyncio.gather(*tasks)

        logger.info(f"Stored {success_total}/{total} embeddings in Manticore (table={table_name})")
        return success_total == total

    async def search_similar_embeddings(
        self,
        query_embedding: List[float],
        table_name: str = "embeddings",
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        在Manticore Search中搜索相似向量
        注意：这是一个简化版本，实际的向量相似度搜索需要更复杂的实现
        """
        if not self.manticore_enabled:
            logger.warning("Manticore Search not available")
            return []

        try:
            # 这里使用文本搜索作为示例，实际应该使用向量相似度搜索
            # 在生产环境中，需要实现KNN搜索或使用Manticore的向量搜索功能
            results = self.manticore_client.search_documents(
                table_name=table_name,
                query="*",  # 获取所有文档作为示例
                limit=limit
            )

            logger.info(f"Found {len(results)} similar embeddings")
            return results

        except Exception as e:
            logger.error(f"Failed to search similar embeddings: {e}")
            return []

    async def _ensure_embedding_table_exists(self, table_name: str) -> bool:
        """
        确保向量表存在
        """
        try:
            if not self.manticore_client.table_exists(table_name):
                # 获取配置的向量维度
                from manticore_search.utils.config import get_settings
                manticore_settings = get_settings()
                vector_dims = manticore_settings.vector_dimensions

                # 创建向量表的schema - 使用float_vector而非text
                schema = f"""
                id bigint,
                text text,
                embedding float_vector knn_type='hnsw' knn_dims='{vector_dims}' hnsw_similarity='cosine',
                dimension int,
                created_at timestamp default now()
                """

                self.manticore_client.create_table(table_name, schema)
                logger.info(f"Created embedding table: {table_name} with {vector_dims} dimensions")

            return True

        except Exception as e:
            logger.error(f"Failed to ensure table exists: {e}")
            return False

    # 向后兼容的方法 (已弃用)
    async def embed_text_legacy(self, request: LegacyEmbeddingRequest) -> LegacyEmbeddingResponse:
        """
        向后兼容的单文本向量化方法 (已弃用)
        """
        logger.warning("Using deprecated embed_text_legacy method")

        embedding = await self.embed_single_text(
            text=request.text,
            model=request.model
        )

        return LegacyEmbeddingResponse(
            embedding=embedding,
            dimension=len(embedding),
            processing_time=0.0,  # 不再计算处理时间
            model=request.model or self.settings.default_model
        )
