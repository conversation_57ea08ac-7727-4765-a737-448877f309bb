# Master Know 项目蓝图

## 项目概述

Master Know 是一个基于 FastAPI 的全栈知识管理平台，集成了 Manticore Search 和 Dramatiq 任务队列，旨在提供高效的知识存储、检索和管理功能。

## 技术栈

### 后端
- **FastAPI**: 现代、快速的 Web 框架
- **SQLAlchemy**: ORM 数据库操作
- **PostgreSQL**: 主数据库
- **Manticore Search**: 全文搜索引擎
- **Dramatiq**: 异步任务队列
- **Redis**: 缓存和消息代理
- **Pydantic**: 数据验证和序列化

### 前端
- **React**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript
- **Vite**: 构建工具
- **TanStack Query**: 数据获取和状态管理

### 部署和运维
- **Docker**: 容器化
- **Docker Compose**: 本地开发环境
- **uv**: Python 包管理
- **Traefik**: 反向代理和负载均衡

## 开发阶段规划

### 阶段一：基础架构搭建
1. **项目初始化**
   - 使用 copier 基于 fastapi/full-stack-fastapi-template 模板生成新项目仓库
   - 配置基本的项目结构和依赖

2. **环境验证**
   - 在本地通过 docker compose watch 成功运行所有原生服务
   - 确保能访问后端 API 和前端页面

3. **配置管理策略**
   - 采用混合注入方案：开发环境使用 .env，生产环境通过云平台注入环境变量
   - 记录详细步骤

4. **集成 Manticore**
   - 在 docker-compose.yml 中添加 Manticore 服务
   - 编写一个异步客户端

5. **集成 Dramatiq**
   - 添加 Dramatiq worker 服务
   - 配置 Redis 为 broker
   - 创建一个简单的异步测试任务

### 阶段二：核心功能开发
1. **用户认证和授权**
   - 扩展现有的用户系统
   - 实现基于角色的访问控制

2. **知识条目管理**
   - 设计知识条目数据模型
   - 实现 CRUD 操作
   - 支持富文本内容

3. **全文搜索功能**
   - 集成 Manticore Search
   - 实现索引同步机制
   - 提供高级搜索功能

4. **异步任务处理**
   - 实现内容索引任务
   - 文档处理和转换
   - 批量操作支持

### 阶段三：高级功能
1. **知识图谱**
   - 实现条目间的关联关系
   - 可视化知识网络

2. **协作功能**
   - 多用户协作编辑
   - 版本控制和历史记录

3. **AI 集成**
   - 智能内容推荐
   - 自动标签生成
   - 内容摘要

### 阶段四：优化和部署
1. **性能优化**
   - 数据库查询优化
   - 缓存策略
   - 前端性能优化

2. **监控和日志**
   - 集成监控系统
   - 错误追踪
   - 性能指标收集

3. **生产部署**
   - CI/CD 流水线
   - 容器编排
   - 安全加固

## 项目结构

```
master-know/
├── backend/                 # FastAPI 后端
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── tasks/          # Dramatiq 任务
│   └── tests/              # 后端测试
├── frontend/               # React 前端
│   ├── src/
│   │   ├── components/     # React 组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API 服务
│   │   └── utils/          # 工具函数
│   └── tests/              # 前端测试
├── docs/                   # 项目文档
├── docker-compose.yml      # 开发环境配置
├── docker-compose.override.yml
└── .env                    # 环境变量
```

## 开发规范

### 代码规范
- 后端遵循 PEP 8 和 FastAPI 最佳实践
- 前端使用 ESLint 和 Prettier
- 所有代码必须通过类型检查

### 测试策略
- 单元测试覆盖率 > 80%
- 集成测试覆盖关键业务流程
- E2E 测试覆盖主要用户场景

### 文档要求
- API 文档自动生成
- 代码注释完整
- 架构决策记录 (ADR)

## 部署策略

### 开发环境
- 使用 Docker Compose 进行本地开发
- 支持热重载和调试

### 测试环境
- 自动化部署到测试环境
- 集成测试和性能测试

### 生产环境
- 容器化部署
- 负载均衡和高可用
- 监控和告警

## 风险评估

### 技术风险
- Manticore Search 集成复杂度
- 异步任务处理的可靠性
- 前后端数据同步

### 缓解措施
- 充分的技术调研和原型验证
- 完善的错误处理和重试机制
- 详细的测试覆盖

## 时间规划

- 阶段一：2 周
- 阶段二：4 周
- 阶段三：4 周
- 阶段四：2 周

总计：12 周完成 MVP 版本
